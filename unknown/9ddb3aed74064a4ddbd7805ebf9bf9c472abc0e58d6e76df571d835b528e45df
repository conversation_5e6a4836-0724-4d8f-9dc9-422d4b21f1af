workspace(name = "augment")

load("//tools/bzl:deps/deps.bzl", "setup_deps")

setup_deps()

load("//third_party/cuda:cuda.bzl", "register_cuda_toolchains")

register_cuda_toolchains()

load("//third_party/scann:scann_deps.bzl", "setup_scann_deps")

setup_scann_deps()

load("@remote_cuda_toolchain//:repositories.bzl", "cuda_remote_repos")

cuda_remote_repos()

load("//tools/docker:base_images.bzl", "pull_base_containers")

pull_base_containers()

load("@hedron_compile_commands//:workspace_setup.bzl", "hedron_compile_commands_setup")

hedron_compile_commands_setup()

load("@aspect_bazel_lib//lib:repositories.bzl", "register_jq_toolchains", "register_tar_toolchains")

register_jq_toolchains()

register_tar_toolchains()

# helm rules

load("//tools/bzl:deps/helm.bzl", "setup_helm")

setup_helm()

load("//tools/bzl:deps/archive.bzl", "setup_archive")

setup_archive()

load("@aspect_rules_lint//lint:ruff.bzl", "fetch_ruff")

fetch_ruff("0.6.1")

load("//clients/beachhead/img:base-images.bzl", "remote_agents_base_images_setup")

remote_agents_base_images_setup()
