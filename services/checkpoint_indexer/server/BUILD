load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:rust.bzl", "rust_binary", "rust_oci_image", "rust_test")

rust_binary(
    name = "server",
    srcs = glob(["*.rs"]),
    aliases = aliases(),
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    deps = all_crate_deps(
        normal = True,
    ) + [
        "//base/blob_names:blob_names_rs_proto",
        "//base/blob_names/rust:blob_names",
        "//base/feature_flags:feature_flags_rs",
        "//base/logging:struct_logging_rs",
        "//base/metrics_server/rust:metrics_server",
        "//base/rust/numpy",
        "//base/rust/tracing-tonic",
        "//services/auth/central/server:auth_entities_rs_proto",
        "//services/checkpoint_indexer:checkpoint_indexer_rs_proto",
        "//services/content_manager:content_manager_rs_proto",
        "//services/content_manager/client:client_rs",
        "//services/lib/grpc/auth:grpc_auth",
        "//services/lib/grpc/metrics:grpc_metrics",
        "//services/lib/grpc/service:grpc_service",
        "//services/lib/grpc/tls_config:grpc_tls_config_rs",
        "//services/lib/request_context:request_context_rs",
        "//services/request_insight/publisher:publisher_rs",
        "//services/tenant_watcher/client:client_rs",
        "//services/token_exchange/client:client_rs",
        "//third_party/scann_rs",
    ],
)

rust_test(
    name = "server_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":server",
    data = glob(["test_data/*.npy"]),
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

rust_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/lib/pubsub:pubsub-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)
