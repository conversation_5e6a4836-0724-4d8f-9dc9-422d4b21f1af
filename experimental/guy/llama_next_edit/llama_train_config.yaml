determined:
  name: "llama3-next-edit-v0"
  description: null
  workspace: Dev
  project: guy

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 64
  project_group: "finetuning"

fastbackward_configs:
  - configs/llama3_70b.py

fastbackward_args:
  block_size: 8192
  learning_rate: 5e-6
  min_lr: 1.25e-6
  decay_lr: True
  weight_decay: 0.1
  warmup_iters: 0
  train_data_path: /mnt/efs/augment/data/processed/next-edit/gh_pr_train_repartitioned/S1.8_keep_most_6000p_2000f,R1.0_no_retrieval,P1.10_llama3base_context12/train
  eval_data_path: /mnt/efs/augment/data/processed/next-edit/gh_pr_train_repartitioned/S1.8_keep_most_6000p_2000f,R1.0_no_retrieval,P1.10_llama3base_context12/valid
  model_vocab_size: 128256
  batch_size: 2
  gradient_accumulation_steps: 16
  wandb_project: llama3-next-edit
  run_name: llama3-base-next-edit-v0
  checkpoint_dir: /mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-70B-fb
  # checkpoint_dir: /mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-70B-Instruct-fb

  ## Want these
  loss_mask_policy: negative_tokens
  visualize_logits_samples: 8
  tokenizer_name: llama3_base
  use_research_tokenizer: false
  checkpoint_optimizer_state: false
  log_interval: 100
  max_iters: 8000
  max_epochs: 0
  # With these params, eval takes 42 minutes, training 15 minutes.
  # Changing to the parameters below to make it go much faster.
  # eval_interval: 500
  # eval_items: 25600
  eval_interval: 2000
  eval_items: 12800

  ## Want these?
  # fim_middle_token_id: 2
  # eot_token_id: 0
  # pad_token_id: 49152

  # gradient_accumulation_steps: 8
  # batch_size: 2
  # warmup_iters: 80
  # lr_decay_iters: 8000
  # learning_rate: 1e-05
  # min_lr: 1.0000000000000002e-06

  ## Don't need these
  # checkpoint: /mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp2
  # model_vocab_size: 51200
  # train_data_path: /mnt/efs/augment/data/processed/next-edit/100K_repos/S1.7_keep_most_1400p_2000f,R1.0_no_retrieval,P1.8_star2_context12/train
  # eval_data_path: /mnt/efs/augment/data/processed/next-edit/100K_repos/S1.7_keep_most_1400p_2000f,R1.0_no_retrieval,P1.8_star2_context12/valid
  # wandb_project: next-edit-gen
