"""Postprocess the manually collected datasets to do the following:

1. Disentangle the files across the whole dataset.
    - this reduces the size of the dataset by ~20x and is much easier to work with.
2. Group the entries by their "group id".
3. Add a "no diff" entry for each group.

Usage:
> python3 experimental/arun/next_edits/postprocess_dataset.py -i ~/manual.v3.jsonl.zst

This will generate ~/manual.v3.diffs.jsonl.zst and ~/manual.v3.files.jsonl.zst.
"""

import argparse
import dataclasses
import logging
from itertools import groupby
from pathlib import Path

from research.core.diff_utils import CommandFailedError, compute_repo_diff
from research.core.types import compute_file_id
from research.eval.harness import utils
from research.next_edits.next_edits_dataset import Datum, GroupedDatum

logger = logging.getLogger(__name__)


def replace_files_by_ids(files: list[dict], file_store: dict[str, dict]) -> list[str]:
    """Replace `files` with IDs in `file_store`.

    Args:
        files: The list of files in the data.
        file_store: The file store to use. Any new files in `files` will be added to
        `file_store`.

    Returns:
        The list of file IDs.
    """
    updated_files = []
    for file in files:
        file_id = compute_file_id(file["path"], file["contents"])
        if file_id not in file_store:
            file_store[file_id] = {"id": file_id, **file}
        updated_files.append(file_id)
    return updated_files


def disentangle_files(data: list[dict]):
    """Disentangle the files in the data.

    Args:
        data: The data to disentangle.

    Returns:
        A tuple of (files, data) where `files` is a list of dicts with the file
        contents and `data` is the data with the file IDs.
    """
    files = {}
    for grouped_datum in data:
        for datum in grouped_datum["data"]:
            datum["wip_files"] = replace_files_by_ids(datum["wip_files"], files)
    return list(files.values()), data


def group_data(data: list[Datum]) -> list[GroupedDatum]:
    """Group the data by group ID."""
    groups = [
        GroupedDatum(
            group_id=key,
            data=sorted(
                group, key=lambda d: (d.group_sequence_id, len(d.past_to_wip_diff))
            ),
        )
        # groups are already sorted by group_id
        for key, group in groupby(data, lambda d: d.group_id)
    ]
    return groups


def add_empty_diff_entries(grouped_diff: GroupedDatum) -> GroupedDatum | None:
    """Add an empty diff entry to the group."""
    # Applying diffs can fail.
    try:
        wip_to_future_diff = compute_repo_diff(
            grouped_diff.past_repo(), grouped_diff.future_repo()
        )
    except CommandFailedError:
        return None
    new_group = [
        Datum(
            id=f"{grouped_diff.group_id}:0",
            instruction=grouped_diff.data[0].instruction,
            past_to_wip_diff="",
            wip_to_future_diff=str(wip_to_future_diff),
            wip_files=[file for file in grouped_diff.past_repo().files],
            commit_meta=grouped_diff.data[0].commit_meta,
            group_id=grouped_diff.group_id,
            group_sequence_id=0,
        )
    ]
    for i, datum in enumerate(grouped_diff.data):
        new_group.append(
            dataclasses.replace(
                datum,
                id=f"{datum.group_id}:{i + 1}",
                group_sequence_id=i + 1,
            )
        )
    return GroupedDatum(
        group_id=grouped_diff.group_id,
        data=new_group,
    )


def main(input_path: Path, output_diffs_path: Path, output_files_path: Path):
    logger.info("Loading data...")
    data = [Datum.schema().load(datum) for datum in utils.read_jsonl_zst(input_path)]

    logger.info("Grouping data...")
    grouped_data = group_data(data)
    logger.info("Adding diff data...")
    grouped_data = [
        updated_datum
        for grouped_datum in grouped_data
        if (updated_datum := add_empty_diff_entries(grouped_datum))
    ]
    grouped_data = [grouped_datum.to_dict() for grouped_datum in grouped_data]
    logger.info("Disentangling diff data...")
    files, diffs = disentangle_files(grouped_data)

    logger.info("Writing data...")
    utils.write_jsonl_zst(output_diffs_path, diffs)
    utils.write_jsonl_zst(output_files_path, files)


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    parser = argparse.ArgumentParser()
    parser.add_argument("-i", "--input", type=Path)

    args = parser.parse_args()
    input_path: Path = args.input

    assert input_path.name.endswith(".jsonl.zst")
    output_diffs_path = input_path.with_name(
        input_path.name[: -len(".jsonl.zst")] + ".diffs.jsonl.zst"
    )
    output_files_path = input_path.with_name(
        input_path.name[: -len(".jsonl.zst")] + ".files.jsonl.zst"
    )
    main(input_path, output_diffs_path, output_files_path)
