#%%
%load_ext autoreload
%autoreload 2
#%%
import os
import pathlib
os.environ['CUDA_VISIBLE_DEVICES'] = '0,1,2,3'
import transformers
from research.models.core import get_model

print(transformers.__version__)
# ckp_dir = pathlib.Path("/mnt/efs/augment/checkpoints/llama/llama-2-13b-hf")

ckp_dir = pathlib.Path("/home/<USER>/checkpoints/llama-2-70b-hf")
model = get_model("llama2-70b-pretrain-hf", checkpoint_path=ckp_dir)

print(model)
model.load()
#%%
from research.models.core import StopCriteria, GenerationOptions
from research.core.model_input import ModelInput

prompt = r"""
Try your best to coherently and concisely complete the following function
The codes star by the 10 repetitive char of >.
After you finish the completion, please output 10 repetitive char of < indicating finish.
Here is an example:
>>>>>>>>>>
def hello_world():
    print("Hello World!")

class Pen:
    def __init__(self, length: float):
        self._length = length
<<<<<<<<<<

Ok, now let's start.

>>>>>>>>>>
from dataclasses import dataclass

@dataclass
class Turtle:
    '''The turtle can move forward, backward, and turn 90 degrees.'''

    def __init__(
"""
result = model.generate(
    ModelInput(prefix=prompt), GenerationOptions(temperature=0.0, max_generated_tokens=1024)
)
#%%
print(result)