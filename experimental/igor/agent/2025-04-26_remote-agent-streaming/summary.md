# Implementing Chat History Streaming with Incremental Updates for Remote Agents

## Overview

This document summarizes the research findings on implementing streaming functionality for remote agent chat history, replacing the current client-side polling approach. The proposed architecture involves moving the polling logic from the client to the server, with the server streaming updates to the client as they become available. The implementation includes support for incremental updates similar to how the chat system handles RAW_RESPONSE nodes, allowing for real-time updates of agent responses as they are generated.

## Current Implementation

The remote agents feature currently uses client-side polling to fetch updates:

- **Frontend Polling**:
  - Implemented in `RemoteAgentsModel` class in `clients/common/webviews/src/apps/remote-agent-manager/models/remote-agents-model.ts`
  - Uses two polling intervals:
    - 1 second for chat history (`HISTORY_POLLING_INTERVAL`)
    - 5 seconds for agent overviews (`OVERVIEWS_POLLING_INTERVAL`)
  - Separate 500ms polling for agent logs during setup

- **Backend Polling Endpoint**:
  - `ChatHistory` method in `services/remote_agents/server/server.go`
  - Client sends `lastProcessedSequenceId` to get only new updates
  - Server reads chat history from BigTable with sequence IDs greater than the last processed ID

- **Data Storage**:
  - Uses BigTable with specific row key patterns:
    - `RemoteAgent#{agentID}` - Agent config and status
    - `UserAgentMapping#{userID}` - User to agent mappings
    - `ExchangeHistory#{agentID}#{sequenceID}` - Chat history (most relevant for streaming implementation)
    - `UpdateSequenceID#{agentID}` - Latest sequence ID
    - `PendingUpdates#{agentID}#{sequenceID}` - Pending updates

## Proposed Streaming Architecture

### Backend Changes

1. **New Streaming Endpoint**:
   - Add a new `ChatHistoryStream` RPC method to the `RemoteAgents` service
   - Define new request and response message types for streaming
   - Implement the streaming handler in the server
   - Add the endpoint to the API proxy

2. **Server-Side Polling**:
   - Implement server-side polling of chat history in the remote agents service
   - Poll BigTable for new chat history entries at regular intervals
   - Stream updates to the client as they become available
   - Use the existing sequence ID mechanism to track which updates have been sent

3. **Incremental Updates and Structured Nodes**:
   - Define different node types for structured updates (CHAT_HISTORY_EXCHANGE, EXCHANGE_COMPLETE, AGENT_STATUS)
   - Track the last sent content for each exchange to support incremental text updates
   - Send only the new content since the last update to reduce bandwidth usage
   - Allow for partial responses to be displayed while the full response is being generated
   - Implement a mechanism similar to RAW_RESPONSE nodes in chat streaming
   - Ensure proper handling of markdown formatting in streamed text
   - Send completion notifications when an exchange is finished

### Frontend Changes

1. **RemoteAgentsModel Updates**:
   - Replace polling timers with a streaming connection for chat history
   - Implement methods to establish and maintain the streaming connection
   - Add handlers for processing different types of streamed updates:
     - Incremental text updates for existing exchanges
     - New exchange nodes
     - Exchange completion notifications
     - Agent status updates
   - Implement logic to append incremental text updates to existing exchanges
   - Update state management to handle both incremental updates and structured nodes
   - Implement reconnection logic for handling disconnections
   - Provide a smooth typewriter-like effect for displaying incremental updates

2. **Client API Changes**:
   - Add new streaming methods to the `AugmentAPI` class
   - Define new message types for structured nodes and incremental updates
   - Implement similar patterns to the existing chat streaming methods
   - Add support for handling different node types (CHAT_HISTORY_EXCHANGE, EXCHANGE_COMPLETE, AGENT_STATUS)
   - Implement parsing and processing of incremental text updates
   - Maintain backward compatibility for clients still using polling

## Implementation Considerations

### Security

- Maintain authentication and authorization checks for streaming endpoints
- Ensure users can only access their own agent data
- Implement rate limiting and timeouts for streaming connections
- Add proper validation for all streamed data

### Error Handling and Reconnection

- Implement robust error handling for streaming connections
- Add reconnection logic for handling disconnections with exponential backoff
- Provide fallback mechanisms for when streaming is not available
- Handle various interrupt and resume scenarios:
  - Stream breaks during an exchange: Re-stream the current exchange with incremental updates
  - Stream breaks between exchanges: Re-stream from the last processed sequence ID
  - Client explicitly interrupts agent: Properly mark exchanges as interrupted
  - Agent continues execution while client is disconnected: Track and send all new exchanges

### Performance and Scalability

- Monitor connection count and resource usage
- Optimize message size and frequency
- Consider the impact of long-lived connections on server resources

### BigTable Persistence

- **ExchangeHistory Records**:
  - Row key format: `ExchangeHistory#{agentID}#{sequenceID}` with sequenceID padded to 20 digits
  - Stored in the `Output` column family, `output` column
  - Contains serialized `AgentOutput` protocol buffer with the entire exchange
  - Each exchange includes request, response, sequence ID, and other metadata

- **Write Pattern**:
  - No coalescing of writes - each LLM chunk overwrites the previous version
  - Complete exchange is written each time, not just the incremental update
  - Uses `SetCell` mutation which replaces any existing value
  - This approach is simple but not optimized for incremental updates

- **Streaming Implementation Approach**:
  - Server polls BigTable for updates to exchanges
  - Compares new content with last content sent to client
  - Sends only the difference as an incremental update
  - Client appends new content to what it already has
  - Server tracks which content has been sent to each client

- **Potential Future Improvements**:
  - **Append-Only Storage**: Store incremental updates as separate cells with timestamps
  - **Batch Updates**: Coalesce multiple small updates before writing to BigTable
  - **Differential Storage**: Store only the differences between versions
  - These optimizations would require significant changes to the storage layer

## Implementation Plan

### Phase 1: Backend Changes

1. Define new protocol messages for chat history streaming
2. Implement the streaming endpoint in the remote_agent service
3. Add server-side polling of chat history
4. Add the endpoint to the API proxy
5. Implement incremental updates for chat history

### Phase 2: Frontend Changes

1. Add streaming client API methods
2. Update RemoteAgentsModel to use streaming for chat history
3. Implement reconnection and error handling logic
4. Maintain backward compatibility
5. Update UI components as needed

### Phase 3: Testing and Stabilization

1. Test streaming functionality in various scenarios
2. Monitor performance and resource usage
3. Address any issues discovered during testing
4. Gradually roll out to production

## Conclusion

Implementing chat history streaming with incremental updates for remote agents is feasible with the existing codebase structure and would provide significant benefits in terms of responsiveness and resource usage. By moving the polling logic from the client to the server and implementing incremental updates, we can reduce the number of requests, minimize bandwidth usage, and provide a more efficient and responsive user experience.

The changes required are focused and manageable, with most of the complexity in the backend implementation of the streaming endpoint and the frontend adaptation of the RemoteAgentsModel. The existing chat streaming implementation, particularly how it handles RAW_RESPONSE nodes, provides a good reference for many of the required patterns.

The addition of incremental text updates will significantly improve the user experience by providing immediate feedback as the agent generates responses. This approach, already proven in the chat implementation, creates a more engaging and responsive interface that helps users feel the agent is actively working on their requests.

This implementation would align with the goal of moving to a more efficient and responsive architecture for remote agents while enhancing the user experience through real-time, incremental updates.
