"""Test for api_lib."""

import os
import pytest
import tempfile
from unittest.mock import MagicMock, patch
from research.data.synthetic_code_edit.api_lib import (
    GptEmbeddingWrapper,
    GPT_EMBEDDING_PRICES,
)
from openai.types.create_embedding_response import CreateEmbeddingResponse


@patch("openai.embeddings")
def test_generate_embedding(mock_embedding):
    mock_response = MagicMock()
    embedding_response = [1.0, 2.0, 3.0]
    model_name = "text-embedding-ada-002"
    mock_response.configure_mock(
        data=[MagicMock(embedding=embedding_response, index=0, object="embedding")],
        model=model_name,
        object="list",
        usage=MagicMock(prompt_tokens=1, total_tokens=1),
    )
    mock_embedding.create.return_value = mock_response

    with tempfile.NamedTemporaryFile() as temp_file:
        gpt_embedding_wrapper = GptEmbeddingWrapper(
            shareable_between_processes=False, cache_file=temp_file.name
        )

        text = "This is a test string"
        result = gpt_embedding_wrapper(text)
        assert result == embedding_response
        assert mock_embedding.create.call_count == 1
        mock_embedding.create.assert_called_with(input=text, model=model_name)

        # new call
        reversed_text = text[::-1]
        assert text != reversed_text
        result = gpt_embedding_wrapper(reversed_text)
        assert result == embedding_response
        assert mock_embedding.create.call_count == 2
        mock_embedding.create.assert_called_with(input=reversed_text, model=model_name)

        # return cached results
        original_call_count = mock_embedding.create.call_count
        result = gpt_embedding_wrapper(text)
        assert result == embedding_response
        assert mock_embedding.create.call_count == original_call_count

        # called only twice
        get_stats = gpt_embedding_wrapper.get_stats()
        assert get_stats[model_name] == GPT_EMBEDDING_PRICES[model_name] * 2.0
