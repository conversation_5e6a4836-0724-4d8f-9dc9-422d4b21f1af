import { isCustomEvent, mockHost } from "$common-webviews/mocks/hosts/mock-host";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";

mockHost.addEventListener("message-from-webview", async (e: Event) => {
  if (!isCustomEvent(e)) {
    console.error("Unexpected event type: ", e);
    throw new Error("Unexpected mock message");
  }

  switch (e.detail.type) {
    case WebViewMessageType.asyncWrapper: {
      const baseMsg = e.detail.baseMsg;
      switch (baseMsg.type) {
        case WebViewMessageType.resolveFileRequest: {
          const request = baseMsg.data;
          switch (request.relPath) {
            case "./example/known-file.ts": {
              mockHost.sendMessageToWebView({
                type: WebViewMessageType.asyncWrapper,
                requestId: e.detail.requestId,
                error: null,
                baseMsg: {
                  type: WebViewMessageType.resolveFileResponse,
                  data: {
                    repoRoot: "/home/<USER>/example-repo-root",
                    pathName: "./exmaple/known-file.ts",
                  },
                },
              });
              break;
            }
            default:
              // Unknown file response
              console.warn("Unknown file requested: ", request.relPath);
              mockHost.sendMessageToWebView({
                type: WebViewMessageType.asyncWrapper,
                requestId: e.detail.requestId,
                error: null,
                baseMsg: {
                  type: WebViewMessageType.resolveFileResponse,
                  data: null,
                },
              });
              break;
          }
          break;
        }
        case WebViewMessageType.chatLoaded: {
          mockHost.sendMessageToWebView({
            type: WebViewMessageType.asyncWrapper,
            requestId: e.detail.requestId,
            error: null,
            baseMsg: {
              type: WebViewMessageType.chatInitialize,
              data: {},
            },
          });
          break;
        }
        default: {
          console.error("Unexpected async message type: ", baseMsg.type);
          break;
        }
      }
      break;
    }
    case WebViewMessageType.augmentLink: {
      console.log("augment link received: ", e.detail.data);
      break;
    }
    case WebViewMessageType.openFile: {
      console.log("open file received: ", e.detail.data);
      break;
    }
    default:
      console.error(`Unexpected message type: ${e.detail.type}`);
      break;
  }
});
