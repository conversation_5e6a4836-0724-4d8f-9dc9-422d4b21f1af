# ProGuard configuration for Feature Vector Collector Kotlin Library
# This configuration ensures safe obfuscation while preserving necessary functionality

# ================================
# BASIC CONFIGURATION
# ================================

# Don't warn about missing classes - we're building a library
-dontwarn **

# Keep line numbers for debugging
-keepattributes SourceFile,LineNumberTable

# Keep annotations
-keepattributes *Annotation*

# Keep generic signatures for reflection
-keepattributes Signature

# Keep inner classes information
-keepattributes InnerClasses,EnclosingMethod

# ================================
# KOTLIN-SPECIFIC RULES
# ================================

# Keep Kotlin metadata for reflection and serialization
-keepattributes RuntimeVisibleAnnotations,RuntimeInvisibleAnnotations
-keepattributes RuntimeVisibleParameterAnnotations,RuntimeInvisibleParameterAnnotations

# Keep Kotlin coroutines
-keepclassmembers class kotlinx.coroutines.** { *; }
-keep class kotlinx.coroutines.** { *; }
-dontwarn kotlinx.coroutines.**

# Keep Kotlin standard library
-keep class kotlin.** { *; }
-keep class kotlin.Metadata { *; }
-keepclassmembers class **$WhenMappings {
    <fields>;
}

# Keep Kotlin data classes and their methods
-keep class * {
    @kotlin.jvm.JvmField <fields>;
}

# ================================
# LIBRARY PUBLIC API - MINIMAL
# ================================

# Only keep the main entry point for creating feature vectors
-keep class com.augmentcode.featurevector.IntelliJFeatureVectorCollector {
    public static ** createFeatures(...);
}

# Keep the Features class and its toVector method (return type)
-keep class com.augmentcode.featurevector.Features {
    public java.util.Map toVector();
}

# Everything else should be obfuscated - no other public APIs needed

# ================================
# GSON SERIALIZATION
# ================================

# Keep Gson classes
-keep class com.google.gson.** { *; }
-dontwarn com.google.gson.**

# Keep classes that will be serialized/deserialized by Gson
-keep class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# Keep generic type information for Gson
-keepattributes Signature
-keep class * implements java.lang.reflect.Type

# ================================
# OSHI LIBRARY
# ================================

# Keep OSHI classes and their methods
-keep class oshi.** { *; }
-dontwarn oshi.**

# Keep JNA classes used by OSHI
-keep class com.sun.jna.** { *; }
-dontwarn com.sun.jna.**

# ================================
# SYSTEM UTILITIES - OBFUSCATE ALL
# ================================

# SystemUtils and SystemInformationCollector should be completely obfuscated
# They are internal implementation details

# ================================
# REFLECTION AND RUNTIME
# ================================

# Keep classes that might be accessed via reflection
-keepclassmembers class * {
    @kotlin.jvm.JvmStatic *;
}

# Keep enum classes and their values
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# ================================
# OPTIMIZATION SETTINGS
# ================================

# Enable all optimizations except those that might break functionality
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*

# Number of optimization passes
-optimizationpasses 3

# Allow access modification for better optimization
-allowaccessmodification

# ================================
# OBFUSCATION SETTINGS
# ================================

# Use meaningful names for debugging (can be disabled for production)
-keepattributes SourceFile,LineNumberTable

# Don't obfuscate class names that might be used in error messages
-keep class * extends java.lang.Exception {
    *;
}

# ================================
# ADDITIONAL SAFETY RULES
# ================================

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep classes with main methods
-keepclasseswithmembers public class * {
    public static void main(java.lang.String[]);
}

# Keep Serializable classes
-keepnames class * implements java.io.Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
