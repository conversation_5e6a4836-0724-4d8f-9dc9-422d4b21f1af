buildscript {
    repositories {
        gradlePluginPortal()
        mavenCentral()
    }
    dependencies {
        classpath("com.guardsquare:proguard-gradle:7.7.0")
    }
}

plugins {
    kotlin("jvm") version "1.9.22"
    `java-library`
    id("com.github.johnrengelman.shadow") version "8.1.1"
}

group = "com.augmentcode"
version = "1.0.0"

repositories {
    mavenCentral()
    maven("https://www.jetbrains.com/intellij-repository/releases")
    maven("https://cache-redirector.jetbrains.com/intellij-dependencies")
}

dependencies {
    // Kotlin standard library
    implementation("org.jetbrains.kotlin:kotlin-stdlib")

    // OSHI for system information (equivalent to systeminformation npm package)
    implementation("com.github.oshi:oshi-core:6.4.8")

    // Gson for JSON serialization (consistent with IntelliJ plugin)
    implementation("com.google.code.gson:gson:2.10.1")

    // Coroutines for async operations (matching IntelliJ plugin version)
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0")

    // IntelliJ Platform APIs (optional - will be available at runtime in IntelliJ plugin)
    // Using provided scope so they don't need to be available at build time
    compileOnly("org.jetbrains:annotations:24.0.1")

    // R8 for better obfuscation (Google's replacement for ProGuard)
    implementation("com.android.tools:r8:8.2.42")

    // Test dependencies
    testImplementation("org.jetbrains.kotlin:kotlin-test")
    testImplementation("org.junit.jupiter:junit-jupiter:5.9.2")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.9.0")
}

tasks.test {
    useJUnitPlatform()
}

kotlin {
    jvmToolchain(21)

    compilerOptions {
        allWarningsAsErrors.set(true)
    }
}

java {
    withSourcesJar()
    withJavadocJar()
}

// Shadow JAR configuration for fat JAR with all dependencies
tasks.shadowJar {
    archiveClassifier.set("all")
    mergeServiceFiles()
}

// ProGuard obfuscation configuration
tasks.register("obfuscateJar", proguard.gradle.ProGuardTask::class) {
    dependsOn("jar")

    // Input: the compiled JAR
    injars("${layout.buildDirectory.get()}/libs/${project.name}-${project.version}.jar")

    // Output: obfuscated JAR
    outjars("${layout.buildDirectory.get()}/obfuscated/${project.name}-${project.version}-obfuscated.jar")

    // Java runtime library
    libraryjars("${System.getProperty("java.home")}/jmods/java.base.jmod(!**.jar;!module-info.class)")
    libraryjars("${System.getProperty("java.home")}/jmods/java.desktop.jmod(!**.jar;!module-info.class)")
    libraryjars("${System.getProperty("java.home")}/jmods/java.management.jmod(!**.jar;!module-info.class)")

    // Project dependencies
    libraryjars(configurations.runtimeClasspath.get())

    // Configuration file
    configuration("proguard-rules.pro")

    // Verbose output for debugging
    verbose()

    // Print mapping for deobfuscation
    printmapping("${layout.buildDirectory.get()}/obfuscated/mapping.txt")
    printseeds("${layout.buildDirectory.get()}/obfuscated/seeds.txt")
    printusage("${layout.buildDirectory.get()}/obfuscated/usage.txt")
}

// Task to build obfuscated library for production use
tasks.register("buildObfuscated") {
    dependsOn("obfuscateJar")
    group = "build"
    description = "Builds an obfuscated version of the library for production use"

    doLast {
        println("Obfuscated JAR created at: ${layout.buildDirectory.get()}/obfuscated/")
        println("Mapping file for deobfuscation: ${layout.buildDirectory.get()}/obfuscated/mapping.txt")
        println("IMPORTANT: Keep the mapping.txt file safe for debugging obfuscated stack traces!")
    }
}

// Task to verify obfuscation worked correctly
tasks.register("verifyObfuscation") {
    dependsOn("obfuscateJar")
    group = "verification"
    description = "Verifies that obfuscation was applied correctly"

    doLast {
        val obfuscatedJar = file("${layout.buildDirectory.get()}/obfuscated/${project.name}-${project.version}-obfuscated.jar")
        val mappingFile = file("${layout.buildDirectory.get()}/obfuscated/mapping.txt")

        if (obfuscatedJar.exists()) {
            println("✓ Obfuscated JAR exists: ${obfuscatedJar.absolutePath}")
            println("✓ JAR size: ${obfuscatedJar.length()} bytes")
        } else {
            throw GradleException("✗ Obfuscated JAR not found!")
        }

        if (mappingFile.exists()) {
            println("✓ Mapping file exists: ${mappingFile.absolutePath}")
            val mappingContent = mappingFile.readText()
            if (mappingContent.contains("->")) {
                println("✓ Obfuscation mapping detected - classes were renamed")
            } else {
                println("⚠ Warning: No class renaming detected in mapping file")
            }
        } else {
            println("⚠ Warning: Mapping file not found")
        }
    }
}
