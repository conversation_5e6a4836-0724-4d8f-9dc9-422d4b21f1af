# IntelliJ Feature Vector Collector (<PERSON>tlin)

Kotlin implementation of the feature vector collector for IntelliJ IDEA and JetBrains IDEs. Provides identical API to the TypeScript version with additional IntelliJ-specific features.

## API Interface

```kotlin
object IntelliJFeatureVectorCollector {
    suspend fun createFeatures(): Features
}

class Features {
    fun toVector(): Map<Int, String>
}
```

## Usage

```kotlin
import com.augmentcode.featurevector.*
import kotlinx.coroutines.runBlocking

runBlocking {
    val features = IntelliJFeatureVectorCollector.createFeatures()
    val vector = features.toVector()
    // vector contains 50 fields (indices 0-49 + checksum at index 12)
}
```

## Feature Vector Field Contents

The feature vector contains exactly **50 fields** (indices 0-49 + checksum). Each field contains a SHA-256 hash of the canonicalized (lowercase, trimmed) source data.

### Index 0: VSCode Version
- **Content**: Empty string (IntelliJ environment, no VSCode)
- **TypeScript equivalent**: `vscode.version`

### Index 1: VSCode Machine ID  
- **Content**: Empty string (IntelliJ environment, no VSCode)
- **TypeScript equivalent**: `vscode.env.machineId`

### Index 2: Operating System
- **Content**: OS family name (e.g., "Windows", "Linux", "Mac OS X")
- **TypeScript equivalent**: `os.type()`

### Index 3: CPU Model
- **Content**: CPU model name (e.g., "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz")
- **TypeScript equivalent**: `cpus[0].model`

### Index 4: Total Memory
- **Content**: Total system memory in bytes as string
- **TypeScript equivalent**: `os.totalmem().toString()`

### Index 5: CPU Count
- **Content**: Number of logical CPU cores as string
- **TypeScript equivalent**: `cpus.length.toString()`

### Index 6: Hostname
- **Content**: System hostname
- **TypeScript equivalent**: `os.hostname()`

### Index 7: Architecture
- **Content**: System architecture (e.g., "x86_64", "amd64")
- **TypeScript equivalent**: `os.machine()`

### Index 8: Username
- **Content**: Current user name
- **TypeScript equivalent**: `os.userInfo().username`

### Index 9: MAC Addresses
- **Content**: Comma-separated list of external network interface MAC addresses
- **TypeScript equivalent**: `getExternalMacAddresses()`

### Index 10: OS Release
- **Content**: OS version information (e.g., "Windows 10", "Ubuntu 22.04")
- **TypeScript equivalent**: `os.release()`

### Index 11: Kernel Version
- **Content**: OS kernel/build version
- **TypeScript equivalent**: `os.version()`

### Index 12: Checksum
- **Content**: "v1#" + SHA-256 hash of all other fields concatenated
- **TypeScript equivalent**: `calculateChecksum(result)`

### Index 13: Telemetry Device ID
- **Content**: Empty string (IntelliJ environment, no VSCode telemetry)
- **TypeScript equivalent**: `getTelemetryDevDeviceId(userDataPath)`

### Index 14: Request ID
- **Content**: UUID generated for this request
- **TypeScript equivalent**: `crypto.randomUUID()`

### Index 15: Random Hash
- **Content**: 32-character random hex string
- **TypeScript equivalent**: `generateRandomHash()`

### Index 16: OS Machine ID
- **Content**: Platform-specific machine identifier
- **TypeScript equivalent**: `machineIdSync()` or platform-specific methods

### Index 17: Home Directory Inode
- **Content**: Home directory path (not actual inode)
- **TypeScript equivalent**: `getInodeNumber(homeDir)`

### Index 18: Project Root Inode
- **Content**: Current working directory path
- **TypeScript equivalent**: `getInodeNumber(process.cwd())`

### Index 19: Git User Email
- **Content**: Git user.email from local or global config
- **TypeScript equivalent**: `getGitUserEmail()`

### Index 20: SSH Public Key
- **Content**: First found SSH public key content
- **TypeScript equivalent**: `getSshPublicKey()`

### Index 21: User Data Path Inode
- **Content**: Empty string (IntelliJ environment, no VSCode user data)
- **TypeScript equivalent**: `getInodeNumber(userDataPath)`

### Index 22: User Data Machine ID
- **Content**: Empty string (IntelliJ environment, no VSCode machine ID)
- **TypeScript equivalent**: `getUserDataMachineId(userDataPath)`

### Index 23: Storage URI Path
- **Content**: Empty string (IntelliJ environment, no VSCode storage)
- **TypeScript equivalent**: `extensionContext.storageUri?.fsPath`

### Index 24: GPU Information
- **Content**: JSON string of GPU details (model, vendor, VRAM)
- **TypeScript equivalent**: `sysInfo.gpuInfo`

### Index 25: Timezone
- **Content**: System timezone identifier
- **TypeScript equivalent**: `sysInfo.timezone`

### Index 26: Disk Layout
- **Content**: JSON string of disk information
- **TypeScript equivalent**: `sysInfo.diskLayout`

### Index 27: System Information
- **Content**: JSON string of system details (manufacturer, model, UUID)
- **TypeScript equivalent**: `sysInfo.systemInfo`

### Index 28: BIOS Information
- **Content**: JSON string of BIOS details
- **TypeScript equivalent**: `sysInfo.biosInfo`

### Index 29: Baseboard Information
- **Content**: JSON string of motherboard details
- **TypeScript equivalent**: `sysInfo.baseboardInfo`

### Index 30: Chassis Information
- **Content**: JSON string of chassis details
- **TypeScript equivalent**: `sysInfo.chassisInfo`

### Index 31: Baseboard Asset Tag
- **Content**: Motherboard asset tag (often empty)
- **TypeScript equivalent**: `sysInfo.baseboardAssetTag`

### Index 32: Chassis Asset Tag
- **Content**: Chassis asset tag (often empty)
- **TypeScript equivalent**: `sysInfo.chassisAssetTag`

### Index 33: CPU Flags
- **Content**: Empty string (OSHI limitation)
- **TypeScript equivalent**: `sysInfo.cpuFlags`

### Index 34: Memory Module Serials
- **Content**: Empty string (OSHI limitation)
- **TypeScript equivalent**: `sysInfo.memoryModuleSerials`

### Index 35: USB Device IDs
- **Content**: Empty string (OSHI limitation)
- **TypeScript equivalent**: `sysInfo.usbDeviceIds`

### Index 36: Audio Device IDs
- **Content**: Comma-separated audio device identifiers
- **TypeScript equivalent**: `sysInfo.audioDeviceIds`

### Index 37: Hypervisor Type
- **Content**: Virtualization platform name (if detected)
- **TypeScript equivalent**: `sysInfo.hypervisorType`

### Index 38: System Boot Time
- **Content**: System boot timestamp in milliseconds
- **TypeScript equivalent**: `sysInfo.systemBootTime`

### Index 39: SSH Known Hosts
- **Content**: Last 5 entries from ~/.ssh/known_hosts
- **TypeScript equivalent**: `sysInfo.sshKnownHosts`

## IntelliJ-Specific Fields (40-49)

### Index 40: IntelliJ Installation UID
- **Content**: Unique identifier for this IntelliJ installation
- **Source**: Generated UUID based on installation paths

### Index 41: IntelliJ Config Path Inode
- **Content**: IntelliJ configuration directory path
- **Source**: Platform-specific config directory

### Index 42: IntelliJ System Path Inode
- **Content**: IntelliJ system/cache directory path
- **Source**: Platform-specific system directory

### Index 43: IntelliJ Plugins Path Inode
- **Content**: IntelliJ plugins directory path
- **Source**: Platform-specific plugins directory

### Index 44: IntelliJ Eval License ID
- **Content**: Evaluation license identifier (if present)
- **Source**: eval/*.evaluation.key files

### Index 45: IntelliJ Permanent License ID
- **Content**: Permanent license identifier (if present)
- **Source**: license.key, options/other.xml files

### Index 46: IntelliJ Recent Projects Hash
- **Content**: Hash of recently opened projects list
- **Source**: options/recentProjects.xml

### Index 47: IntelliJ Installed Plugins Hash
- **Content**: Hash of installed plugin directory names
- **Source**: plugins/ directory listing

### Index 48: IntelliJ JVM Options Hash
- **Content**: Hash of JVM configuration options
- **Source**: idea.vmoptions files

### Index 49: IntelliJ Product Info Hash
- **Content**: Hash of product version and build information
- **Source**: System properties (idea.version.name, etc.)

## Hashing and Canonicalization

All fields (except checksum) are processed through:
1. **Canonicalization**: `input.lowercase().trim()`
2. **SHA-256 Hashing**: Hex-encoded output
3. **Arrays**: Joined with commas, then canonicalized

The checksum (index 12) is: `"v1#" + SHA256(concatenated_sorted_values)`
