package com.augmentcode.featurevector

/**
 * Enum representing the keys for feature vector fields.
 * This exactly mirrors the TypeScript FeatureVectorKey enum.
 */
enum class FeatureVectorKey(val value: Int) {
    VSCODE(0),
    MACHINE_ID(1),
    OS(2),
    CPU(3),
    MEMORY(4),
    NUM_CPUS(5),
    HOSTNAME(6),
    ARCH(7),
    USERNAME(8),
    MAC_ADDRESSES(9),
    OS_RELEASE(10),
    KERNEL_VERSION(11),
    CHECKSUM(12),
    TELEMETRY_DEV_DEVICE_ID(13),
    REQUEST_ID(14),
    RANDOM_HASH(15),
    OS_MACHINE_ID(16),
    HOME_DIRECTORY_INO(17),
    PROJECT_ROOT_INO(18),
    GIT_USER_EMAIL(19),
    SSH_PUBLIC_KEY(20),
    USER_DATA_PATH_INO(21),
    USER_DATA_MACHINE_ID(22),
    STORAGE_URI_PATH(23),
    GPU_INFO(24),
    <PERSON>IM<PERSON>ZON<PERSON>(25),
    DISK_LAYOUT(26),
    SYSTEM_INFO(27),
    BIOS_INFO(28),
    BASEBOARD_INFO(29),
    CHASSIS_INFO(30),
    BASEBOARD_ASSET_TAG(31),
    CHASSIS_ASSET_TAG(32),
    CPU_FLAGS(33),
    MEMORY_MODULE_SERIALS(34),
    USB_DEVICE_IDS(35),
    AUDIO_DEVICE_IDS(36),
    <PERSON>YPERVISOR_TYPE(37),
    SYSTEM_BOOT_TIME(38),
    SSH_KNOWN_HOSTS(39),

    // IntelliJ-specific features (40-47)
    INTELLIJ_INSTALLATION_UID(40),
    INTELLIJ_CONFIG_PATH_INO(41),
    INTELLIJ_SYSTEM_PATH_INO(42),
    INTELLIJ_PLUGINS_PATH_INO(43),
    INTELLIJ_INSTALLED_PLUGINS_HASH(44),
    INTELLIJ_PRODUCT_INFO_HASH(45),
    INTELLIJ_LICENSE_ID(46),
    INTELLIJ_BUILD_NUMBER(47),
    ;

    companion object {
        fun fromValue(value: Int): FeatureVectorKey? {
            return values().find { it.value == value }
        }
    }
}
