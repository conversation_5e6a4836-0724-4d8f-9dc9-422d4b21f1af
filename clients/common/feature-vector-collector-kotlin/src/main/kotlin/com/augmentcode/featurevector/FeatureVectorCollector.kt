package com.augmentcode.featurevector

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.withContext
import oshi.SystemInfo
import oshi.hardware.HardwareAbstractionLayer
import oshi.software.os.OperatingSystem
import java.io.File
import java.util.UUID

/**
 * Main feature vector collector for IntelliJ IDEA and JetBrains IDEs.
 * This collects system-level features and IntelliJ-specific features.
 * VSCode-specific fields are set to empty strings since this runs in IntelliJ.
 */
object IntelliJFeatureVectorCollector {
    private val systemInfo = SystemInfo()
    private val hal: HardwareAbstractionLayer = systemInfo.hardware
    private val os: OperatingSystem = systemInfo.operatingSystem

    /**
     * Create Features object for non-VSCode environments.
     * This is the main entry point that collects system information.
     * VSCode-specific fields are set to empty strings since this runs outside VSCode.
     */
    suspend fun createFeatures(): Features =
        withContext(Dispatchers.IO) {
            coroutineScope {
                // Get basic system information synchronously
                val processors = hal.processor
                val memory = hal.memory

                // Get username
                val username =
                    try {
                        System.getProperty("user.name") ?: ""
                    } catch (e: Exception) {
                        ""
                    }

                // Generate request ID (UUID)
                val requestId = UUID.randomUUID().toString()

                // Generate random hash
                val randomHash = SystemUtils.generateRandomHash()

                // Get OS machine ID (try multiple sources)
                val osMachineId = getOsMachineId()

                // Get home directory inode
                val homeDir = System.getProperty("user.home")
                val homeDirectoryIno = SystemUtils.getInodeNumber(homeDir)

                // Get project root inode - use current working directory
                val projectRootIno = SystemUtils.getInodeNumber(System.getProperty("user.dir"))

                // Get SSH public key (sync, fast)
                val sshPublicKey = SystemUtils.getSshPublicKey()

                // Run async operations concurrently
                val gitUserEmailDeferred = async { SystemUtils.getGitUserEmail() }
                val sysInfoDeferred = async { SystemInformationCollector.getSystemInformation() }
                val intellijInfoDeferred = async { IntelliJCollector.getIntelliJInfo() }

                // Wait for async results
                val gitUserEmail = gitUserEmailDeferred.await()
                val sysInfo = sysInfoDeferred.await()
                val intellijInfo = intellijInfoDeferred.await()

                // Create and return Features object
                Features(
                    // VSCode-specific fields - all empty since we're not running in VSCode
                    vscode = "",
                    machineId = "",
                    telemetryDevDeviceId = "",
                    userDataPathIno = "",
                    userDataMachineId = "",
                    storageUriPath = "",

                    // System-level fields - collected normally
                    os = os.family,
                    cpu = processors.processorIdentifier.name,
                    memory = memory.total.toString(),
                    numCpus = processors.logicalProcessorCount.toString(),
                    hostname =
                        try {
                            java.net.InetAddress.getLocalHost().hostName
                        } catch (e: Exception) {
                            ""
                        },
                    arch = System.getProperty("os.arch"),
                    username = username,
                    macAddresses = SystemUtils.getExternalMacAddresses(),
                    osRelease = "${os.family} ${os.versionInfo.version}",
                    kernelVersion = os.versionInfo.buildNumber ?: "",
                    requestId = requestId,
                    randomHash = randomHash,
                    osMachineId = osMachineId,
                    homeDirectoryIno = homeDirectoryIno,
                    projectRootIno = projectRootIno,
                    gitUserEmail = gitUserEmail,
                    sshPublicKey = sshPublicKey,
                    gpuInfo = sysInfo.gpuInfo,
                    timezone = sysInfo.timezone,
                    diskLayout = sysInfo.diskLayout,
                    systemInfo = sysInfo.systemInfo,
                    biosInfo = sysInfo.biosInfo,
                    baseboardInfo = sysInfo.baseboardInfo,
                    chassisInfo = sysInfo.chassisInfo,
                    baseboardAssetTag = sysInfo.baseboardAssetTag,
                    chassisAssetTag = sysInfo.chassisAssetTag,
                    cpuFlags = sysInfo.cpuFlags,
                    memoryModuleSerials = sysInfo.memoryModuleSerials,
                    usbDeviceIds = sysInfo.usbDeviceIds,
                    audioDeviceIds = sysInfo.audioDeviceIds,
                    hypervisorType = sysInfo.hypervisorType,
                    systemBootTime = sysInfo.systemBootTime,
                    sshKnownHosts = sysInfo.sshKnownHosts,

                    // IntelliJ-specific fields
                    intellijInstallationUid = intellijInfo.installationUid,
                    intellijConfigPathIno = intellijInfo.configPathIno,
                    intellijSystemPathIno = intellijInfo.systemPathIno,
                    intellijPluginsPathIno = intellijInfo.pluginsPathIno,
                    intellijEvalLicenseId = intellijInfo.evalLicenseId,
                    intellijPermanentLicenseId = intellijInfo.permanentLicenseId,
                    intellijRecentProjectsHash = intellijInfo.recentProjectsHash,
                    intellijInstalledPluginsHash = intellijInfo.installedPluginsHash,
                    intellijJvmOptionsHash = intellijInfo.jvmOptionsHash,
                    intellijProductInfoHash = intellijInfo.productInfoHash,
                )
            }
        }

    /**
     * Get OS machine ID, trying multiple sources like the TypeScript version.
     */
    private fun getOsMachineId(): String {
        return try {
            // Try different machine ID sources based on OS
            val osName = System.getProperty("os.name").lowercase()

            when {
                osName.contains("linux") -> {
                    // Try /etc/machine-id first, then /var/lib/dbus/machine-id
                    listOf("/etc/machine-id", "/var/lib/dbus/machine-id").forEach { path ->
                        try {
                            val file = File(path)
                            if (file.exists()) {
                                return file.readText().trim()
                            }
                        } catch (e: Exception) {
                            // Continue to next path
                        }
                    }
                }
                osName.contains("mac") || osName.contains("darwin") -> {
                    // Try to get hardware UUID on macOS
                    try {
                        val process =
                            ProcessBuilder("system_profiler", "SPHardwareDataType")
                                .start()
                        val output = process.inputStream.bufferedReader().readText()
                        val uuidRegex = "Hardware UUID: (.+)".toRegex()
                        val match = uuidRegex.find(output)
                        if (match != null) {
                            return match.groupValues[1].trim()
                        }
                    } catch (e: Exception) {
                        // Fall through to empty string
                    }
                }
                osName.contains("windows") -> {
                    // Try to get machine GUID on Windows
                    try {
                        val process =
                            ProcessBuilder("wmic", "csproduct", "get", "UUID", "/value")
                                .start()
                        val output = process.inputStream.bufferedReader().readText()
                        val uuidRegex = "UUID=(.+)".toRegex()
                        val match = uuidRegex.find(output)
                        if (match != null) {
                            return match.groupValues[1].trim()
                        }
                    } catch (e: Exception) {
                        // Fall through to empty string
                    }
                }
            }
            ""
        } catch (e: Exception) {
            ""
        }
    }


}
