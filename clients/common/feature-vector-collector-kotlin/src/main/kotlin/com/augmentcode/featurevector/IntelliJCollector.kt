package com.augmentcode.featurevector

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths
import java.security.MessageDigest
import java.util.UUID

/**
 * IntelliJ-specific feature collector that extracts unique identifiers and signatures
 * from IntelliJ IDEA and other JetBrains IDEs.
 * 
 * This collector focuses on independent signatures that are unique to IntelliJ installations
 * and avoids highly correlated features.
 */
object IntelliJCollector {
    
    /**
     * Data class representing IntelliJ-specific information.
     */
    data class IntelliJInfo(
        val installationUid: String,
        val configPathIno: String,
        val systemPathIno: String,
        val pluginsPathIno: String,
        val evalLicenseId: String,
        val permanentLicenseId: String,
        val recentProjectsHash: String,
        val installedPluginsHash: String,
        val jvmOptionsHash: String,
        val productInfoHash: String,
    )
    
    /**
     * Collect all IntelliJ-specific information.
     */
    suspend fun getIntelliJInfo(): IntelliJInfo = withContext(Dispatchers.IO) {
        val configPaths = getIntelliJConfigPaths()
        
        IntelliJInfo(
            installationUid = getInstallationUid(configPaths),
            configPathIno = SystemUtils.getInodeNumber(configPaths.configPath),
            systemPathIno = SystemUtils.getInodeNumber(configPaths.systemPath),
            pluginsPathIno = SystemUtils.getInodeNumber(configPaths.pluginsPath),
            evalLicenseId = getEvalLicenseId(configPaths.configPath),
            permanentLicenseId = getPermanentLicenseId(configPaths.configPath),
            recentProjectsHash = getRecentProjectsHash(configPaths.configPath),
            installedPluginsHash = getInstalledPluginsHash(configPaths.pluginsPath),
            jvmOptionsHash = getJvmOptionsHash(),
            productInfoHash = getProductInfoHash(),
        )
    }
    
    /**
     * Data class for IntelliJ configuration paths.
     */
    private data class IntelliJConfigPaths(
        val configPath: String,
        val systemPath: String,
        val pluginsPath: String,
    )
    
    /**
     * Get IntelliJ configuration paths based on the operating system.
     */
    private fun getIntelliJConfigPaths(): IntelliJConfigPaths {
        val osName = System.getProperty("os.name").lowercase()
        val userHome = System.getProperty("user.home")
        
        // Try to detect the most recent IntelliJ version
        val productNames = listOf("IntelliJIdea", "IdeaIC", "PyCharm", "PyCharmCE", "WebStorm", "PhpStorm", "RubyMine", "CLion", "DataGrip", "Rider", "AppCode")
        
        return when {
            osName.contains("windows") -> {
                val appData = System.getenv("APPDATA") ?: "$userHome\\AppData\\Roaming"
                val localAppData = System.getenv("LOCALAPPDATA") ?: "$userHome\\AppData\\Local"
                
                val configPath = findMostRecentPath("$appData\\JetBrains", productNames)
                val systemPath = findMostRecentPath("$localAppData\\JetBrains", productNames)
                
                IntelliJConfigPaths(
                    configPath = configPath,
                    systemPath = systemPath,
                    pluginsPath = "$configPath\\plugins"
                )
            }
            osName.contains("mac") || osName.contains("darwin") -> {
                val configPath = findMostRecentPath("$userHome/Library/Application Support/JetBrains", productNames)
                val systemPath = findMostRecentPath("$userHome/Library/Caches/JetBrains", productNames)
                
                IntelliJConfigPaths(
                    configPath = configPath,
                    systemPath = systemPath,
                    pluginsPath = "$configPath/plugins"
                )
            }
            else -> { // Linux
                val configPath = findMostRecentPath("$userHome/.config/JetBrains", productNames)
                val systemPath = findMostRecentPath("$userHome/.cache/JetBrains", productNames)
                val pluginsPath = findMostRecentPath("$userHome/.local/share/JetBrains", productNames)
                
                IntelliJConfigPaths(
                    configPath = configPath,
                    systemPath = systemPath,
                    pluginsPath = pluginsPath
                )
            }
        }
    }
    
    /**
     * Find the most recent IntelliJ installation path.
     */
    private fun findMostRecentPath(basePath: String, productNames: List<String>): String {
        return try {
            val baseDir = File(basePath)
            if (!baseDir.exists()) return ""
            
            val productDirs = baseDir.listFiles()?.filter { dir ->
                productNames.any { product -> dir.name.startsWith(product) }
            }?.sortedByDescending { it.name } // Sort by name (which includes version)
            
            productDirs?.firstOrNull()?.absolutePath ?: ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Generate a deterministic installation UID based on real IntelliJ installation factors.
     * This creates a unique identifier without writing any files to the user's system.
     */
    private fun getInstallationUid(configPaths: IntelliJConfigPaths): String {
        return try {
            // Use only real, existing installation factors to create a deterministic UID
            val installationFactors = listOf(
                configPaths.configPath,
                configPaths.systemPath,
                configPaths.pluginsPath,
                // Add the actual IntelliJ version from the path (e.g., "WebStorm2024.3")
                configPaths.configPath.substringAfterLast("/").substringAfterLast("\\"),
                // Add system-level factors that are stable for this installation
                System.getProperty("user.home", ""),
                System.getProperty("os.name", ""),
            ).filter { it.isNotEmpty() }.joinToString("|")

            if (installationFactors.isNotEmpty()) {
                UUID.nameUUIDFromBytes(installationFactors.toByteArray()).toString()
            } else {
                ""
            }
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Extract evaluation license ID from IntelliJ configuration.
     */
    private fun getEvalLicenseId(configPath: String): String {
        return try {
            val evalDir = File(configPath, "eval")
            if (!evalDir.exists()) return ""
            
            val evalFiles = evalDir.listFiles()?.filter { it.name.endsWith(".evaluation.key") }
            evalFiles?.firstOrNull()?.nameWithoutExtension?.replace(".evaluation", "") ?: ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Extract permanent license ID from IntelliJ configuration.
     */
    private fun getPermanentLicenseId(configPath: String): String {
        return try {
            // Check for actual license files that exist in IntelliJ installations
            val licenseLocations = listOf(
                File(configPath, "idea.key"),           // Main IntelliJ license
                File(configPath, "pycharm.key"),        // PyCharm license
                File(configPath, "webstorm.key"),       // WebStorm license
                File(configPath, "plugin_PCWMP.license"), // Plugin license
                File(configPath, "options/other.xml")   // License references in config
            )

            for (licenseFile in licenseLocations) {
                if (licenseFile.exists()) {
                    val content = licenseFile.readText()

                    // For binary license files, extract readable license ID patterns
                    val licenseIdPatterns = listOf(
                        // Standard license ID patterns
                        "licenseId[\"=:]\\s*[\"']?([A-Z0-9-]+)[\"']?".toRegex(RegexOption.IGNORE_CASE),
                        "LICENSE_ID[\"=:]\\s*[\"']?([A-Z0-9-]+)[\"']?".toRegex(RegexOption.IGNORE_CASE),
                        // Plugin license patterns (like PF87RO46SQAD31C)
                        "([A-Z0-9]{10,20})".toRegex(),
                        // Certificate key patterns
                        "<certificate-key>\\s*([A-Z0-9-]+)".toRegex(RegexOption.IGNORE_CASE)
                    )

                    for (pattern in licenseIdPatterns) {
                        val match = pattern.find(content)
                        if (match != null && match.groupValues[1].length >= 8) {
                            return match.groupValues[1]
                        }
                    }
                }
            }
            ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Generate hash of recent projects configuration.
     */
    private fun getRecentProjectsHash(configPath: String): String {
        return try {
            val recentProjectsFile = File(configPath, "options/recentProjects.xml")
            if (recentProjectsFile.exists()) {
                val content = recentProjectsFile.readText()
                // Extract project paths and hash them
                val projectPaths = "path=\"([^\"]+)\"".toRegex()
                    .findAll(content)
                    .map { it.groupValues[1] }
                    .sorted()
                    .joinToString("|")
                
                if (projectPaths.isNotEmpty()) {
                    hashString(projectPaths)
                } else ""
            } else ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Generate hash of installed plugins.
     */
    private fun getInstalledPluginsHash(pluginsPath: String): String {
        return try {
            val pluginsDir = File(pluginsPath)
            if (!pluginsDir.exists()) return ""
            
            val pluginNames = pluginsDir.listFiles()
                ?.filter { it.isDirectory }
                ?.map { it.name }
                ?.sorted()
                ?.joinToString("|") ?: ""
            
            if (pluginNames.isNotEmpty()) {
                hashString(pluginNames)
            } else ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Generate hash of JVM options configuration.
     */
    private fun getJvmOptionsHash(): String {
        return try {
            // Look for JVM options in common locations
            val jvmOptionsLocations = listOf(
                System.getProperty("idea.jvm.options.file"),
                System.getProperty("user.home") + "/.idea/idea.vmoptions",
                System.getProperty("user.home") + "/.IntelliJIdea/idea.vmoptions"
            ).filterNotNull()
            
            for (location in jvmOptionsLocations) {
                val file = File(location)
                if (file.exists()) {
                    val content = file.readText()
                        .lines()
                        .filter { it.trim().isNotEmpty() && !it.startsWith("#") }
                        .sorted()
                        .joinToString("|")
                    
                    if (content.isNotEmpty()) {
                        return hashString(content)
                    }
                }
            }
            ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Generate hash of product information.
     */
    private fun getProductInfoHash(): String {
        return try {
            // Try to get product info from system properties
            val productInfo = listOf(
                System.getProperty("idea.vendor.name", ""),
                System.getProperty("idea.version.name", ""),
                System.getProperty("idea.build.number", ""),
                System.getProperty("java.vendor", ""),
                System.getProperty("java.version", "")
            ).filter { it.isNotEmpty() }.joinToString("|")
            
            if (productInfo.isNotEmpty()) {
                hashString(productInfo)
            } else ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Hash a string using SHA-256.
     */
    private fun hashString(input: String): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(input.toByteArray())
            hashBytes.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            ""
        }
    }
}
