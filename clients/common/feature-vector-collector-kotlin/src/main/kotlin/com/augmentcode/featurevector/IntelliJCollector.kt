package com.augmentcode.featurevector

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths
import java.security.MessageDigest
import java.util.UUID

/**
 * IntelliJ-specific feature collector that extracts unique identifiers and signatures
 * from IntelliJ IDEA and other JetBrains IDEs.
 * 
 * This collector focuses on independent signatures that are unique to IntelliJ installations
 * and avoids highly correlated features.
 */
object IntelliJCollector {
    
    /**
     * Data class representing IntelliJ-specific information.
     */
    data class IntelliJInfo(
        val installationUid: String,
        val configPathIno: String,
        val systemPathIno: String,
        val pluginsPathIno: String,
        val evalLicenseId: String,
        val permanentLicenseId: String,
        val recentProjectsHash: String,
        val installedPluginsHash: String,
        val jvmOptionsHash: String,
        val productInfoHash: String,
    )
    
    /**
     * Collect all IntelliJ-specific information.
     */
    suspend fun getIntelliJInfo(): IntelliJInfo = withContext(Dispatchers.IO) {
        val configPaths = getIntelliJConfigPaths()
        
        IntelliJInfo(
            installationUid = getInstallationUid(configPaths),
            configPathIno = SystemUtils.getInodeNumber(configPaths.configPath),
            systemPathIno = SystemUtils.getInodeNumber(configPaths.systemPath),
            pluginsPathIno = SystemUtils.getInodeNumber(configPaths.pluginsPath),
            evalLicenseId = getEvalLicenseId(configPaths.configPath),
            permanentLicenseId = getPermanentLicenseId(configPaths.configPath),
            recentProjectsHash = getRecentProjectsHash(configPaths.configPath),
            installedPluginsHash = getInstalledPluginsHash(configPaths.pluginsPath),
            jvmOptionsHash = getJvmOptionsHash(),
            productInfoHash = getProductInfoHash(),
        )
    }
    
    /**
     * Data class for IntelliJ configuration paths.
     */
    private data class IntelliJConfigPaths(
        val configPath: String,
        val systemPath: String,
        val pluginsPath: String,
    )
    
    /**
     * Get IntelliJ configuration paths based on the operating system.
     */
    private fun getIntelliJConfigPaths(): IntelliJConfigPaths {
        val osName = System.getProperty("os.name").lowercase()
        val userHome = System.getProperty("user.home")
        
        // Try to detect the most recent IntelliJ version
        val productNames = listOf("IntelliJIdea", "IdeaIC", "PyCharm", "PyCharmCE", "WebStorm", "PhpStorm", "RubyMine", "CLion", "DataGrip", "Rider", "AppCode")
        
        return when {
            osName.contains("windows") -> {
                val appData = System.getenv("APPDATA") ?: "$userHome\\AppData\\Roaming"
                val localAppData = System.getenv("LOCALAPPDATA") ?: "$userHome\\AppData\\Local"
                
                val configPath = findMostRecentPath("$appData\\JetBrains", productNames)
                val systemPath = findMostRecentPath("$localAppData\\JetBrains", productNames)
                
                IntelliJConfigPaths(
                    configPath = configPath,
                    systemPath = systemPath,
                    pluginsPath = "$configPath\\plugins"
                )
            }
            osName.contains("mac") || osName.contains("darwin") -> {
                val configPath = findMostRecentPath("$userHome/Library/Application Support/JetBrains", productNames)
                val systemPath = findMostRecentPath("$userHome/Library/Caches/JetBrains", productNames)
                
                IntelliJConfigPaths(
                    configPath = configPath,
                    systemPath = systemPath,
                    pluginsPath = "$configPath/plugins"
                )
            }
            else -> { // Linux
                val configPath = findMostRecentPath("$userHome/.config/JetBrains", productNames)
                val systemPath = findMostRecentPath("$userHome/.cache/JetBrains", productNames)
                val pluginsPath = findMostRecentPath("$userHome/.local/share/JetBrains", productNames)
                
                IntelliJConfigPaths(
                    configPath = configPath,
                    systemPath = systemPath,
                    pluginsPath = pluginsPath
                )
            }
        }
    }
    
    /**
     * Find the most recent IntelliJ installation path.
     */
    private fun findMostRecentPath(basePath: String, productNames: List<String>): String {
        return try {
            val baseDir = File(basePath)
            if (!baseDir.exists()) return ""
            
            val productDirs = baseDir.listFiles()?.filter { dir ->
                productNames.any { product -> dir.name.startsWith(product) }
            }?.sortedByDescending { it.name } // Sort by name (which includes version)
            
            productDirs?.firstOrNull()?.absolutePath ?: ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Generate or retrieve a unique installation UID for this IntelliJ installation.
     * This creates a persistent identifier that's unique to this specific installation.
     */
    private fun getInstallationUid(configPaths: IntelliJConfigPaths): String {
        return try {
            val uidFile = File(configPaths.configPath, ".augment_installation_uid")
            
            if (uidFile.exists()) {
                uidFile.readText().trim()
            } else {
                // Generate a new UID based on installation-specific factors
                val installationFactors = listOf(
                    configPaths.configPath,
                    configPaths.systemPath,
                    configPaths.pluginsPath,
                    System.getProperty("java.home", ""),
                    System.getProperty("user.name", ""),
                ).joinToString("|")
                
                val uid = UUID.nameUUIDFromBytes(installationFactors.toByteArray()).toString()
                
                // Try to persist the UID for future use
                try {
                    File(configPaths.configPath).mkdirs()
                    uidFile.writeText(uid)
                } catch (e: Exception) {
                    // If we can't write, just return the computed UID
                }
                
                uid
            }
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Extract evaluation license ID from IntelliJ configuration.
     */
    private fun getEvalLicenseId(configPath: String): String {
        return try {
            val evalDir = File(configPath, "eval")
            if (!evalDir.exists()) return ""
            
            val evalFiles = evalDir.listFiles()?.filter { it.name.endsWith(".evaluation.key") }
            evalFiles?.firstOrNull()?.nameWithoutExtension?.replace(".evaluation", "") ?: ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Extract permanent license ID from IntelliJ configuration.
     */
    private fun getPermanentLicenseId(configPath: String): String {
        return try {
            // Check for license files in various locations
            val licenseLocations = listOf(
                File(configPath, "license.key"),
                File(configPath, "license"),
                File(configPath, "options/other.xml")
            )
            
            for (licenseFile in licenseLocations) {
                if (licenseFile.exists()) {
                    val content = licenseFile.readText()
                    
                    // Extract license ID from various formats
                    val licenseIdPatterns = listOf(
                        "licenseId=\"([^\"]+)\"".toRegex(),
                        "license-id=\"([^\"]+)\"".toRegex(),
                        "LICENSE_ID=([^\\s]+)".toRegex(),
                        "licenseKey=\"([^\"]+)\"".toRegex()
                    )
                    
                    for (pattern in licenseIdPatterns) {
                        val match = pattern.find(content)
                        if (match != null) {
                            return match.groupValues[1]
                        }
                    }
                }
            }
            ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Generate hash of recent projects configuration.
     */
    private fun getRecentProjectsHash(configPath: String): String {
        return try {
            val recentProjectsFile = File(configPath, "options/recentProjects.xml")
            if (recentProjectsFile.exists()) {
                val content = recentProjectsFile.readText()
                // Extract project paths and hash them
                val projectPaths = "path=\"([^\"]+)\"".toRegex()
                    .findAll(content)
                    .map { it.groupValues[1] }
                    .sorted()
                    .joinToString("|")
                
                if (projectPaths.isNotEmpty()) {
                    hashString(projectPaths)
                } else ""
            } else ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Generate hash of installed plugins.
     */
    private fun getInstalledPluginsHash(pluginsPath: String): String {
        return try {
            val pluginsDir = File(pluginsPath)
            if (!pluginsDir.exists()) return ""
            
            val pluginNames = pluginsDir.listFiles()
                ?.filter { it.isDirectory }
                ?.map { it.name }
                ?.sorted()
                ?.joinToString("|") ?: ""
            
            if (pluginNames.isNotEmpty()) {
                hashString(pluginNames)
            } else ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Generate hash of JVM options configuration.
     */
    private fun getJvmOptionsHash(): String {
        return try {
            // Look for JVM options in common locations
            val jvmOptionsLocations = listOf(
                System.getProperty("idea.jvm.options.file"),
                System.getProperty("user.home") + "/.idea/idea.vmoptions",
                System.getProperty("user.home") + "/.IntelliJIdea/idea.vmoptions"
            ).filterNotNull()
            
            for (location in jvmOptionsLocations) {
                val file = File(location)
                if (file.exists()) {
                    val content = file.readText()
                        .lines()
                        .filter { it.trim().isNotEmpty() && !it.startsWith("#") }
                        .sorted()
                        .joinToString("|")
                    
                    if (content.isNotEmpty()) {
                        return hashString(content)
                    }
                }
            }
            ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Generate hash of product information.
     */
    private fun getProductInfoHash(): String {
        return try {
            // Try to get product info from system properties
            val productInfo = listOf(
                System.getProperty("idea.vendor.name", ""),
                System.getProperty("idea.version.name", ""),
                System.getProperty("idea.build.number", ""),
                System.getProperty("java.vendor", ""),
                System.getProperty("java.version", "")
            ).filter { it.isNotEmpty() }.joinToString("|")
            
            if (productInfo.isNotEmpty()) {
                hashString(productInfo)
            } else ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Hash a string using SHA-256.
     */
    private fun hashString(input: String): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(input.toByteArray())
            hashBytes.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            ""
        }
    }
}
