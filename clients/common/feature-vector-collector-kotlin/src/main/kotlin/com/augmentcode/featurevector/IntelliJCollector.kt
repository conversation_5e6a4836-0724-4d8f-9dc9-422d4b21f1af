package com.augmentcode.featurevector

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths
import java.security.MessageDigest
import java.util.UUID

/**
 * IntelliJ-specific feature collector that extracts unique identifiers and signatures
 * from IntelliJ IDEA and other JetBrains IDEs.
 * 
 * This collector focuses on independent signatures that are unique to IntelliJ installations
 * and avoids highly correlated features.
 */
object IntelliJCollector {
    
    /**
     * Data class representing IntelliJ-specific information.
     * Only includes features that are reliably collectible.
     */
    data class IntelliJInfo(
        val installationUid: String,
        val configPathIno: String,
        val systemPathIno: String,
        val pluginsPathIno: String,
        val installedPluginsHash: String,
        val productInfoHash: String,
        val licenseId: String,
        val buildNumber: String,
    )
    
    /**
     * Collect all IntelliJ-specific information.
     */
    suspend fun getIntelliJInfo(): IntelliJInfo = withContext(Dispatchers.IO) {
        val configPaths = getIntelliJConfigPaths()
        
        IntelliJInfo(
            installationUid = getInstallationUid(configPaths),
            configPathIno = SystemUtils.getInodeNumber(configPaths.configPath),
            systemPathIno = SystemUtils.getInodeNumber(configPaths.systemPath),
            pluginsPathIno = SystemUtils.getInodeNumber(configPaths.pluginsPath),
            installedPluginsHash = getInstalledPluginsHash(configPaths.pluginsPath),
            productInfoHash = getProductInfoFromApi(),
            licenseId = getLicenseIdFromApi(),
            buildNumber = getBuildNumberFromApi(),
        )
    }
    
    /**
     * Data class for IntelliJ configuration paths.
     */
    private data class IntelliJConfigPaths(
        val configPath: String,
        val systemPath: String,
        val pluginsPath: String,
    )
    
    /**
     * Get IntelliJ configuration paths based on the operating system.
     */
    private fun getIntelliJConfigPaths(): IntelliJConfigPaths {
        val osName = System.getProperty("os.name").lowercase()
        val userHome = System.getProperty("user.home")
        
        // Try to detect the most recent IntelliJ version
        val productNames = listOf("IntelliJIdea", "IdeaIC", "PyCharm", "PyCharmCE", "WebStorm", "PhpStorm", "RubyMine", "CLion", "DataGrip", "Rider", "AppCode")
        
        return when {
            osName.contains("windows") -> {
                val appData = System.getenv("APPDATA") ?: "$userHome\\AppData\\Roaming"
                val localAppData = System.getenv("LOCALAPPDATA") ?: "$userHome\\AppData\\Local"
                
                val configPath = findMostRecentPath("$appData\\JetBrains", productNames)
                val systemPath = findMostRecentPath("$localAppData\\JetBrains", productNames)
                
                IntelliJConfigPaths(
                    configPath = configPath,
                    systemPath = systemPath,
                    pluginsPath = "$configPath\\plugins"
                )
            }
            osName.contains("mac") || osName.contains("darwin") -> {
                val configPath = findMostRecentPath("$userHome/Library/Application Support/JetBrains", productNames)
                val systemPath = findMostRecentPath("$userHome/Library/Caches/JetBrains", productNames)
                
                IntelliJConfigPaths(
                    configPath = configPath,
                    systemPath = systemPath,
                    pluginsPath = "$configPath/plugins"
                )
            }
            else -> { // Linux
                val configPath = findMostRecentPath("$userHome/.config/JetBrains", productNames)
                val systemPath = findMostRecentPath("$userHome/.cache/JetBrains", productNames)
                val pluginsPath = findMostRecentPath("$userHome/.local/share/JetBrains", productNames)
                
                IntelliJConfigPaths(
                    configPath = configPath,
                    systemPath = systemPath,
                    pluginsPath = pluginsPath
                )
            }
        }
    }
    
    /**
     * Find the most recent IntelliJ installation path.
     */
    private fun findMostRecentPath(basePath: String, productNames: List<String>): String {
        return try {
            val baseDir = File(basePath)
            if (!baseDir.exists()) return ""
            
            val productDirs = baseDir.listFiles()?.filter { dir ->
                productNames.any { product -> dir.name.startsWith(product) }
            }?.sortedByDescending { it.name } // Sort by name (which includes version)
            
            productDirs?.firstOrNull()?.absolutePath ?: ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Generate a deterministic installation UID based on real IntelliJ installation factors.
     * This creates a unique identifier without writing any files to the user's system.
     */
    private fun getInstallationUid(configPaths: IntelliJConfigPaths): String {
        return try {
            // Use only real, existing installation factors to create a deterministic UID
            val installationFactors = listOf(
                configPaths.configPath,
                configPaths.systemPath,
                configPaths.pluginsPath,
                // Add the actual IntelliJ version from the path (e.g., "WebStorm2024.3")
                configPaths.configPath.substringAfterLast("/").substringAfterLast("\\"),
                // Add system-level factors that are stable for this installation
                System.getProperty("user.home", ""),
                System.getProperty("os.name", ""),
            ).filter { it.isNotEmpty() }.joinToString("|")

            if (installationFactors.isNotEmpty()) {
                UUID.nameUUIDFromBytes(installationFactors.toByteArray()).toString()
            } else {
                ""
            }
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Get license ID using IntelliJ Platform API (when available).
     */
    private fun getLicenseIdFromApi(): String {
        return try {
            // Use reflection to access IntelliJ APIs when available
            val applicationManagerClass = Class.forName("com.intellij.openapi.application.ApplicationManager")
            val getApplicationMethod = applicationManagerClass.getMethod("getApplication")
            val application = getApplicationMethod.invoke(null)

            if (application != null) {
                val applicationInfoClass = Class.forName("com.intellij.openapi.application.ApplicationInfo")
                val getInstanceMethod = applicationInfoClass.getMethod("getInstance")
                val appInfo = getInstanceMethod.invoke(null)

                // Get build information
                val getBuildMethod = applicationInfoClass.getMethod("getBuild")
                val build = getBuildMethod.invoke(appInfo)

                val buildClass = build.javaClass
                val asStringMethod = buildClass.getMethod("asString")
                val getProductCodeMethod = buildClass.getMethod("getProductCode")

                val buildNumber = asStringMethod.invoke(build) as String
                val productCode = getProductCodeMethod.invoke(build) as String

                val getVersionNameMethod = applicationInfoClass.getMethod("getVersionName")
                val versionName = getVersionNameMethod.invoke(appInfo) as String

                // Create a deterministic license identifier based on available info
                hashString("$productCode-$buildNumber-$versionName")
            } else {
                ""
            }
        } catch (e: Exception) {
            // IntelliJ APIs not available, fallback to empty
            ""
        }
    }

    /**
     * Get build number using IntelliJ Platform API (when available).
     */
    private fun getBuildNumberFromApi(): String {
        return try {
            // Use reflection to access IntelliJ APIs when available
            val applicationManagerClass = Class.forName("com.intellij.openapi.application.ApplicationManager")
            val getApplicationMethod = applicationManagerClass.getMethod("getApplication")
            val application = getApplicationMethod.invoke(null)

            if (application != null) {
                val applicationInfoClass = Class.forName("com.intellij.openapi.application.ApplicationInfo")
                val getInstanceMethod = applicationInfoClass.getMethod("getInstance")
                val appInfo = getInstanceMethod.invoke(null)

                val getBuildMethod = applicationInfoClass.getMethod("getBuild")
                val build = getBuildMethod.invoke(appInfo)

                val asStringMethod = build.javaClass.getMethod("asString")
                asStringMethod.invoke(build) as String
            } else {
                ""
            }
        } catch (e: Exception) {
            // IntelliJ APIs not available, fallback to empty
            ""
        }
    }

    /**
     * Get product information using IntelliJ Platform API (when available).
     */
    private fun getProductInfoFromApi(): String {
        return try {
            // Use reflection to access IntelliJ APIs when available
            val applicationManagerClass = Class.forName("com.intellij.openapi.application.ApplicationManager")
            val getApplicationMethod = applicationManagerClass.getMethod("getApplication")
            val application = getApplicationMethod.invoke(null)

            if (application != null) {
                val applicationInfoClass = Class.forName("com.intellij.openapi.application.ApplicationInfo")
                val getInstanceMethod = applicationInfoClass.getMethod("getInstance")
                val appInfo = getInstanceMethod.invoke(null)

                // Get various product information
                val getVersionNameMethod = applicationInfoClass.getMethod("getVersionName")
                val getFullVersionMethod = applicationInfoClass.getMethod("getFullVersion")
                val getCompanyNameMethod = applicationInfoClass.getMethod("getCompanyName")
                val getBuildMethod = applicationInfoClass.getMethod("getBuild")

                val versionName = getVersionNameMethod.invoke(appInfo) as String
                val fullVersion = getFullVersionMethod.invoke(appInfo) as String
                val companyName = getCompanyNameMethod.invoke(appInfo) as String
                val build = getBuildMethod.invoke(appInfo)

                val getProductCodeMethod = build.javaClass.getMethod("getProductCode")
                val asStringMethod = build.javaClass.getMethod("asString")
                val productCode = getProductCodeMethod.invoke(build) as String
                val buildString = asStringMethod.invoke(build) as String

                val productInfo = listOf(
                    versionName,
                    fullVersion,
                    productCode,
                    buildString,
                    companyName
                ).filter { it.isNotEmpty() }.joinToString("|")

                if (productInfo.isNotEmpty()) {
                    hashString(productInfo)
                } else ""
            } else {
                // Fallback to system properties if not running in IntelliJ
                getProductInfoFromSystemProperties()
            }
        } catch (e: Exception) {
            // Fallback to system properties
            getProductInfoFromSystemProperties()
        }
    }
    
    /**
     * Fallback method to get product info from system properties.
     */
    private fun getProductInfoFromSystemProperties(): String {
        return try {
            val productInfo = listOf(
                System.getProperty("idea.vendor.name", ""),
                System.getProperty("idea.version.name", ""),
                System.getProperty("idea.build.number", ""),
                System.getProperty("java.vendor", ""),
                System.getProperty("java.version", "")
            ).filter { it.isNotEmpty() }.joinToString("|")

            if (productInfo.isNotEmpty()) {
                hashString(productInfo)
            } else ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * Generate hash of installed plugins.
     */
    private fun getInstalledPluginsHash(pluginsPath: String): String {
        return try {
            val pluginsDir = File(pluginsPath)
            if (!pluginsDir.exists()) return ""
            
            val pluginNames = pluginsDir.listFiles()
                ?.filter { it.isDirectory }
                ?.map { it.name }
                ?.sorted()
                ?.joinToString("|") ?: ""
            
            if (pluginNames.isNotEmpty()) {
                hashString(pluginNames)
            } else ""
        } catch (e: Exception) {
            ""
        }
    }
    

    
    /**
     * Hash a string using SHA-256.
     */
    private fun hashString(input: String): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(input.toByteArray())
            hashBytes.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            ""
        }
    }
}
