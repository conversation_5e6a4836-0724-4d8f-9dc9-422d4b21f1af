package com.augmentcode.featurevector

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * Integration test for the IntelliJ Feature Vector Collector.
 * Verifies end-to-end functionality and validates the complete feature vector output.
 */
class IntelliJFeatureVectorCollectorIntegrationTest {

    @Test
    fun `should create complete feature vector with 50 fields`() = runBlocking {
        val features = IntelliJFeatureVectorCollector.createFeatures()
        val vector = features.toVector()
        
        assertEquals(50, vector.size, "Vector should have exactly 50 fields")
        
        // Verify all indices 0-49 are present
        for (i in 0..49) {
            assertTrue(vector.containsKey(i), "Vector should contain index $i")
        }
    }

    @Test
    fun `should have valid checksum format`() = runBlocking {
        val features = IntelliJFeatureVectorCollector.createFeatures()
        val vector = features.toVector()
        
        val checksum = vector[FeatureVectorKey.CHECKSUM.value]!!
        assertTrue(checksum.startsWith("v1#"), "Checksum should start with 'v1#'")
        assertEquals(67, checksum.length, "Checksum should be 67 characters (v1# + 64 hex chars)")
    }

    @Test
    fun `should have empty VSCode fields in IntelliJ environment`() = runBlocking {
        val features = IntelliJFeatureVectorCollector.createFeatures()
        val vector = features.toVector()
        
        val emptyStringHash = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
        
        // VSCode-specific fields should be hashed empty strings
        assertEquals(emptyStringHash, vector[FeatureVectorKey.VSCODE.value])
        assertEquals(emptyStringHash, vector[FeatureVectorKey.MACHINE_ID.value])
        assertEquals(emptyStringHash, vector[FeatureVectorKey.TELEMETRY_DEV_DEVICE_ID.value])
        assertEquals(emptyStringHash, vector[FeatureVectorKey.USER_DATA_PATH_INO.value])
        assertEquals(emptyStringHash, vector[FeatureVectorKey.USER_DATA_MACHINE_ID.value])
        assertEquals(emptyStringHash, vector[FeatureVectorKey.STORAGE_URI_PATH.value])
    }

    @Test
    fun `should populate system-level fields`() = runBlocking {
        val features = IntelliJFeatureVectorCollector.createFeatures()
        val vector = features.toVector()
        
        // System fields should be populated (not empty string hash)
        val emptyStringHash = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
        
        assertNotNull(vector[FeatureVectorKey.OS.value])
        assertTrue(vector[FeatureVectorKey.OS.value] != emptyStringHash, "OS should be populated")
        
        assertNotNull(vector[FeatureVectorKey.CPU.value])
        assertTrue(vector[FeatureVectorKey.CPU.value] != emptyStringHash, "CPU should be populated")
        
        assertNotNull(vector[FeatureVectorKey.HOSTNAME.value])
        assertTrue(vector[FeatureVectorKey.HOSTNAME.value] != emptyStringHash, "Hostname should be populated")
        
        assertNotNull(vector[FeatureVectorKey.USERNAME.value])
        assertTrue(vector[FeatureVectorKey.USERNAME.value] != emptyStringHash, "Username should be populated")
    }

    @Test
    fun `should include IntelliJ-specific fields`() = runBlocking {
        val features = IntelliJFeatureVectorCollector.createFeatures()
        val vector = features.toVector()
        
        // IntelliJ fields should be present (may be empty if no IntelliJ installation)
        assertNotNull(vector[FeatureVectorKey.INTELLIJ_INSTALLATION_UID.value])
        assertNotNull(vector[FeatureVectorKey.INTELLIJ_CONFIG_PATH_INO.value])
        assertNotNull(vector[FeatureVectorKey.INTELLIJ_SYSTEM_PATH_INO.value])
        assertNotNull(vector[FeatureVectorKey.INTELLIJ_PLUGINS_PATH_INO.value])
        assertNotNull(vector[FeatureVectorKey.INTELLIJ_EVAL_LICENSE_ID.value])
        assertNotNull(vector[FeatureVectorKey.INTELLIJ_PERMANENT_LICENSE_ID.value])
        assertNotNull(vector[FeatureVectorKey.INTELLIJ_RECENT_PROJECTS_HASH.value])
        assertNotNull(vector[FeatureVectorKey.INTELLIJ_INSTALLED_PLUGINS_HASH.value])
        assertNotNull(vector[FeatureVectorKey.INTELLIJ_JVM_OPTIONS_HASH.value])
        assertNotNull(vector[FeatureVectorKey.INTELLIJ_PRODUCT_INFO_HASH.value])
    }

    @Test
    fun `should hash all field values as 64-character hex strings`() = runBlocking {
        val features = IntelliJFeatureVectorCollector.createFeatures()
        val vector = features.toVector()
        
        vector.forEach { (index, value) ->
            if (index != FeatureVectorKey.CHECKSUM.value) {
                assertTrue(
                    value.matches(Regex("^[a-f0-9]{64}$")), 
                    "Field $index should be a 64-character hex hash, but was: '$value'"
                )
            }
        }
    }

    @Test
    fun `should generate unique request ID and random hash`() = runBlocking {
        val features1 = IntelliJFeatureVectorCollector.createFeatures()
        val features2 = IntelliJFeatureVectorCollector.createFeatures()
        
        val vector1 = features1.toVector()
        val vector2 = features2.toVector()
        
        // Request IDs should be different
        assertTrue(
            vector1[FeatureVectorKey.REQUEST_ID.value] != vector2[FeatureVectorKey.REQUEST_ID.value],
            "Request IDs should be unique between calls"
        )
        
        // Random hashes should be different
        assertTrue(
            vector1[FeatureVectorKey.RANDOM_HASH.value] != vector2[FeatureVectorKey.RANDOM_HASH.value],
            "Random hashes should be unique between calls"
        )
    }

    @Test
    fun `should have consistent checksums for same data`() = runBlocking {
        val features = IntelliJFeatureVectorCollector.createFeatures()
        val vector1 = features.toVector()
        val vector2 = features.toVector()
        
        // Converting the same features object to vector should produce identical results
        assertEquals(vector1, vector2, "Same features should produce identical vectors")
    }
}
