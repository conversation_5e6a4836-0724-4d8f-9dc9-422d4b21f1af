package com.augmentcode.featurevector

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class FeaturesTest {
    @Test
    fun testFeatureVectorKeyValues() {
        // Test that enum values match TypeScript implementation
        assertEquals(0, FeatureVectorKey.VSCODE.value)
        assertEquals(1, FeatureVectorKey.MACHINE_ID.value)
        assertEquals(12, FeatureVectorKey.CHECKSUM.value)
        assertEquals(39, FeatureVectorKey.SSH_KNOWN_HOSTS.value)
    }

    @Test
    fun testCanonicalize() {
        val features = createTestFeatures()

        // Test canonicalize function
        val result1 = features.canonicalize("Test String")
        val result2 = features.canonicalize("test string")
        val result3 = features.canonicalize("  TEST STRING  ")

        // All should produce the same hash (lowercase, trimmed)
        assertEquals(result1, result2)
        assertEquals(result2, result3)

        // Should be a 64-character hex string (SHA-256)
        assertEquals(64, result1.length)
        assertTrue(result1.matches(Regex("[0-9a-f]{64}")))
    }

    @Test
    fun testCanonicalizeArray() {
        val features = createTestFeatures()

        val array1 = listOf("Apple", "Banana", "Cherry")
        val array2 = listOf("apple", "banana", "cherry")
        val array3 = listOf("  APPLE  ", "  BANANA  ", "  CHERRY  ")

        val result1 = features.canonicalizeArray(array1)
        val result2 = features.canonicalizeArray(array2)
        val result3 = features.canonicalizeArray(array3)

        // All should produce the same hash
        assertEquals(result1, result2)
        assertEquals(result2, result3)

        // Should be a 64-character hex string
        assertEquals(64, result1.length)
        assertTrue(result1.matches(Regex("[0-9a-f]{64}")))
    }

    @Test
    fun testToVector() {
        val features = createTestFeatures()
        val vector = features.toVector()

        // Should contain all expected keys
        assertEquals(50, vector.size) // 49 feature keys + 1 checksum

        // Check that all enum values are present
        FeatureVectorKey.values().forEach { key ->
            assertTrue(vector.containsKey(key.value), "Missing key: ${key.name}")
            assertNotNull(vector[key.value], "Null value for key: ${key.name}")
        }

        // Checksum should be present and have correct format
        val checksum = vector[FeatureVectorKey.CHECKSUM.value]
        assertNotNull(checksum)
        assertTrue(checksum.startsWith("v1#"))
        assertEquals(67, checksum.length) // "v1#" + 64 hex chars
    }

    @Test
    fun testCalculateChecksum() {
        val features = createTestFeatures()
        val vector =
            mapOf(
                0 to "test1",
                1 to "test2",
                2 to "test3",
            )

        val checksum = features.calculateChecksum(vector)

        // Should have correct format
        assertTrue(checksum.startsWith("v1#"))
        assertEquals(67, checksum.length)

        // Should be deterministic
        val checksum2 = features.calculateChecksum(vector)
        assertEquals(checksum, checksum2)
    }

    @Test
    fun testSystemUtilsBasicFunctions() {
        // Test that basic system utilities work
        val macAddresses = SystemUtils.getExternalMacAddresses()
        assertNotNull(macAddresses)

        val randomHash = SystemUtils.generateRandomHash()
        assertEquals(32, randomHash.length) // 16 bytes = 32 hex chars
        assertTrue(randomHash.matches(Regex("[0-9a-f]{32}")))

        val sshKey = SystemUtils.getSshPublicKey()
        assertNotNull(sshKey) // May be empty if no SSH key exists

        val sshHosts = SystemUtils.getSshKnownHosts()
        assertNotNull(sshHosts) // May be empty if no known_hosts file exists
    }

    @Test
    fun testFeatureVectorCollector() =
        runBlocking {
            val features = IntelliJFeatureVectorCollector.createFeatures()

            // Verify VSCode-specific properties are empty (since we're not in VSCode)
            assertEquals("", features.vscode)
            assertEquals("", features.machineId)
            assertEquals("", features.telemetryDevDeviceId)
            assertEquals("", features.userDataPathIno)
            assertEquals("", features.userDataMachineId)
            assertEquals("", features.storageUriPath)

            // Verify system-level properties are set
            assertNotNull(features.os)
            assertNotNull(features.cpu)
            assertNotNull(features.hostname)
            assertNotNull(features.arch)
            assertNotNull(features.requestId)
            assertEquals(32, features.randomHash.length)

            // Verify IntelliJ-specific properties are set (may be empty if no IntelliJ installation found)
            assertNotNull(features.intellijInstallationUid)
            assertNotNull(features.intellijConfigPathIno)
            assertNotNull(features.intellijSystemPathIno)
            assertNotNull(features.intellijPluginsPathIno)
            assertNotNull(features.intellijEvalLicenseId)
            assertNotNull(features.intellijPermanentLicenseId)
            assertNotNull(features.intellijRecentProjectsHash)
            assertNotNull(features.intellijInstalledPluginsHash)
            assertNotNull(features.intellijJvmOptionsHash)
            assertNotNull(features.intellijProductInfoHash)

            // Test that vector generation works
            val vector = features.toVector()
            assertEquals(50, vector.size) // Updated to include 10 new IntelliJ features
        }

    private fun createTestFeatures(): Features {
        return Features(
            // VSCode-specific fields - empty since we're not in VSCode
            vscode = "",
            machineId = "",
            telemetryDevDeviceId = "",
            userDataPathIno = "",
            userDataMachineId = "",
            storageUriPath = "",

            // System-level fields
            os = "Linux",
            cpu = "Intel Core i7",
            memory = "16777216000",
            numCpus = "8",
            hostname = "test-host",
            arch = "x86_64",
            username = "testuser",
            macAddresses = listOf("00:11:22:33:44:55"),
            osRelease = "Ubuntu 22.04",
            kernelVersion = "5.15.0",
            requestId = "test-request-id",
            randomHash = "0123456789abcdef0123456789abcdef",
            osMachineId = "test-os-machine-id",
            homeDirectoryIno = "/home/<USER>",
            projectRootIno = "/home/<USER>/project",
            gitUserEmail = "<EMAIL>",
            sshPublicKey = "ssh-rsa AAAAB3...",
            gpuInfo = "{}",
            timezone = "UTC",
            diskLayout = "{}",
            systemInfo = "{}",
            biosInfo = "{}",
            baseboardInfo = "{}",
            chassisInfo = "{}",
            baseboardAssetTag = "",
            chassisAssetTag = "",
            cpuFlags = "",
            memoryModuleSerials = "",
            usbDeviceIds = "",
            audioDeviceIds = "",
            hypervisorType = "",
            systemBootTime = 1640995200000L,
            sshKnownHosts = "",

            // IntelliJ-specific fields - empty for test
            intellijInstallationUid = "",
            intellijConfigPathIno = "",
            intellijSystemPathIno = "",
            intellijPluginsPathIno = "",
            intellijEvalLicenseId = "",
            intellijPermanentLicenseId = "",
            intellijRecentProjectsHash = "",
            intellijInstalledPluginsHash = "",
            intellijJvmOptionsHash = "",
            intellijProductInfoHash = "",
        )
    }
}
