package com.augmentcode.featurevector

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * Tests for the IntelliJ-specific feature collection logic.
 */
class IntelliJCollectorTest {

    @Test
    fun `should collect IntelliJ info without throwing exceptions`() = runBlocking {
        val intellijInfo = IntelliJCollector.getIntelliJInfo()
        
        // Should not throw and should return valid object
        assertNotNull(intellijInfo)
        assertNotNull(intellijInfo.installationUid)
        assertNotNull(intellijInfo.configPathIno)
        assertNotNull(intellijInfo.systemPathIno)
        assertNotNull(intellijInfo.pluginsPathIno)
        assertNotNull(intellijInfo.installedPluginsHash)
        assertNotNull(intellijInfo.productInfoHash)
        assertNotNull(intellijInfo.licenseId)
        assertNotNull(intellijInfo.buildNumber)
    }

    @Test
    fun `should generate deterministic installation UID`() = runBlocking {
        val info1 = IntelliJCollector.getIntelliJInfo()
        val info2 = IntelliJCollector.getIntelliJInfo()
        
        // Installation UID should be consistent across calls
        assertEquals(info1.installationUid, info2.installationUid, 
            "Installation UID should be deterministic")
        
        // Should be a valid UUID format if not empty
        if (info1.installationUid.isNotEmpty()) {
            assertTrue(info1.installationUid.matches(
                Regex("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}")
            ), "Installation UID should be valid UUID format")
        }
    }

    @Test
    fun `should handle missing IntelliJ installation gracefully`() = runBlocking {
        // This test verifies the collector doesn't crash when IntelliJ isn't found
        // We can't easily simulate this, but we can verify the current behavior
        val intellijInfo = IntelliJCollector.getIntelliJInfo()
        
        // Even if IntelliJ isn't found, should return valid (possibly empty) data
        assertNotNull(intellijInfo)
        
        // Count how many fields have actual data
        val fieldsWithData = listOf(
            intellijInfo.installationUid,
            intellijInfo.configPathIno,
            intellijInfo.systemPathIno,
            intellijInfo.pluginsPathIno,
            intellijInfo.installedPluginsHash,
            intellijInfo.productInfoHash,
            intellijInfo.licenseId,
            intellijInfo.buildNumber
        ).count { it.isNotEmpty() }
        
        // Should have at least some data if IntelliJ is installed, or all empty if not
        assertTrue(fieldsWithData >= 0, "Should handle missing IntelliJ gracefully")
    }

    @Test
    fun `should generate consistent hashes for same plugin data`() = runBlocking {
        val info1 = IntelliJCollector.getIntelliJInfo()
        val info2 = IntelliJCollector.getIntelliJInfo()
        
        // Plugin hashes should be consistent
        assertEquals(info1.installedPluginsHash, info2.installedPluginsHash,
            "Plugin hash should be deterministic")
        
        // Product info hash should be consistent
        assertEquals(info1.productInfoHash, info2.productInfoHash,
            "Product info hash should be deterministic")
    }

    @Test
    fun `should validate hash formats for non-empty fields`() = runBlocking {
        val intellijInfo = IntelliJCollector.getIntelliJInfo()
        
        // Check that non-empty hash fields are valid SHA-256 hashes
        if (intellijInfo.installedPluginsHash.isNotEmpty()) {
            assertTrue(intellijInfo.installedPluginsHash.matches(Regex("^[a-f0-9]{64}$")),
                "Installed plugins hash should be valid SHA-256")
        }
        
        if (intellijInfo.productInfoHash.isNotEmpty()) {
            assertTrue(intellijInfo.productInfoHash.matches(Regex("^[a-f0-9]{64}$")),
                "Product info hash should be valid SHA-256")
        }
        
        if (intellijInfo.licenseId.isNotEmpty()) {
            assertTrue(intellijInfo.licenseId.matches(Regex("^[a-f0-9]{64}$")),
                "License ID should be valid SHA-256")
        }
    }

    @Test
    fun `should handle IntelliJ Platform API availability`() = runBlocking {
        val intellijInfo = IntelliJCollector.getIntelliJInfo()
        
        // License ID and build number depend on IntelliJ Platform APIs
        // When running standalone (not as IntelliJ plugin), these should be empty
        // When running as plugin, these might have data
        
        // Both scenarios are valid - just verify no exceptions are thrown
        assertNotNull(intellijInfo.licenseId)
        assertNotNull(intellijInfo.buildNumber)
        
        // If we're not in IntelliJ (which we're not during tests), these should be empty
        assertEquals("", intellijInfo.licenseId, 
            "License ID should be empty when not running in IntelliJ")
        assertEquals("", intellijInfo.buildNumber,
            "Build number should be empty when not running in IntelliJ")
    }

    @Test
    fun `should detect real IntelliJ installation when present`() = runBlocking {
        val intellijInfo = IntelliJCollector.getIntelliJInfo()
        
        // If any path fields are populated, we found an IntelliJ installation
        val hasIntelliJInstallation = listOf(
            intellijInfo.configPathIno,
            intellijInfo.systemPathIno,
            intellijInfo.pluginsPathIno
        ).any { it.isNotEmpty() }
        
        if (hasIntelliJInstallation) {
            // If we found IntelliJ, installation UID should also be present
            assertTrue(intellijInfo.installationUid.isNotEmpty(),
                "Installation UID should be present when IntelliJ is detected")
            
            // At least one of the path fields should contain actual paths
            assertTrue(
                intellijInfo.configPathIno.contains("JetBrains") ||
                intellijInfo.systemPathIno.contains("JetBrains") ||
                intellijInfo.pluginsPathIno.contains("JetBrains"),
                "Path fields should contain JetBrains directory references"
            )
        }
    }

    @Test
    fun `should maintain data consistency across multiple calls`() = runBlocking {
        // Call multiple times to ensure consistency
        val results = (1..3).map { IntelliJCollector.getIntelliJInfo() }
        
        // All results should be identical (deterministic)
        results.forEach { result ->
            assertEquals(results[0].installationUid, result.installationUid)
            assertEquals(results[0].configPathIno, result.configPathIno)
            assertEquals(results[0].systemPathIno, result.systemPathIno)
            assertEquals(results[0].pluginsPathIno, result.pluginsPathIno)
            assertEquals(results[0].installedPluginsHash, result.installedPluginsHash)
            assertEquals(results[0].productInfoHash, result.productInfoHash)
            assertEquals(results[0].licenseId, result.licenseId)
            assertEquals(results[0].buildNumber, result.buildNumber)
        }
    }
}
