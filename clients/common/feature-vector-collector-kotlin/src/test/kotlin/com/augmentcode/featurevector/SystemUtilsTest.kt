package com.augmentcode.featurevector

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * Tests for SystemUtils functionality.
 */
class SystemUtilsTest {

    @Test
    fun `should generate random hash with correct format`() {
        val hash1 = SystemUtils.generateRandomHash()
        val hash2 = SystemUtils.generateRandomHash()
        
        // Should be 32 hex characters
        assertEquals(32, hash1.length)
        assertEquals(32, hash2.length)
        
        // Should be valid hex
        assertTrue(hash1.matches(Regex("^[0-9a-f]{32}$")))
        assertTrue(hash2.matches(Regex("^[0-9a-f]{32}$")))
        
        // Should be different each time
        assertTrue(hash1 != hash2, "Random hashes should be unique")
    }

    @Test
    fun `should get MAC addresses without throwing`() {
        val macAddresses = SystemUtils.getExternalMacAddresses()
        
        assertNotNull(macAddresses)
        // MAC addresses list can be empty on some systems, but should not be null
        
        // If we have MAC addresses, they should be valid format
        macAddresses.forEach { mac ->
            assertTrue(mac.matches(Regex("^[0-9a-f]{2}:[0-9a-f]{2}:[0-9a-f]{2}:[0-9a-f]{2}:[0-9a-f]{2}:[0-9a-f]{2}$")),
                "MAC address should be in valid format: $mac")
        }
    }

    @Test
    fun `should get inode number for valid paths`() {
        val homeDir = System.getProperty("user.home")
        val homeInode = SystemUtils.getInodeNumber(homeDir)
        
        assertNotNull(homeInode)
        // On most systems, this should return the actual path (not a real inode number)
        // since OSHI doesn't provide real inode access
        assertTrue(homeInode.isNotEmpty(), "Should return non-empty result for valid path")
    }

    @Test
    fun `should handle invalid paths gracefully`() {
        val invalidPath = "/this/path/definitely/does/not/exist/anywhere"
        val result = SystemUtils.getInodeNumber(invalidPath)
        
        assertNotNull(result)
        // Should not throw exception, may return empty string or the path itself
    }

    @Test
    fun `should get SSH public key without throwing`() {
        val sshKey = SystemUtils.getSshPublicKey()
        
        assertNotNull(sshKey)
        // SSH key may be empty if no key exists, but should not be null
        
        // If we have an SSH key, it should start with a valid prefix
        if (sshKey.isNotEmpty()) {
            assertTrue(
                sshKey.startsWith("ssh-rsa") || 
                sshKey.startsWith("ssh-ed25519") || 
                sshKey.startsWith("ssh-dss") ||
                sshKey.startsWith("ecdsa-sha2"),
                "SSH key should start with valid algorithm prefix"
            )
        }
    }

    @Test
    fun `should get SSH known hosts without throwing`() {
        val knownHosts = SystemUtils.getSshKnownHosts()
        
        assertNotNull(knownHosts)
        // Known hosts may be empty if file doesn't exist, but should not be null
    }

    @Test
    fun `should get git user email without throwing`() = runBlocking {
        val gitEmail = SystemUtils.getGitUserEmail()

        assertNotNull(gitEmail)
        // Git email may be empty if not configured, but should not be null

        // If we have a git email, it should be a valid email format
        if (gitEmail.isNotEmpty()) {
            assertTrue(gitEmail.contains("@"), "Git email should contain @ symbol")
        }
    }

    @Test
    fun `should handle concurrent access safely`() {
        // Test that multiple threads can call SystemUtils methods safely
        val threads = (1..5).map { _ ->
            Thread {
                repeat(10) {
                    SystemUtils.generateRandomHash()
                    SystemUtils.getExternalMacAddresses()
                    SystemUtils.getInodeNumber(System.getProperty("user.home"))
                }
            }
        }
        
        threads.forEach { it.start() }
        threads.forEach { it.join() }
        
        // If we get here without exceptions, concurrent access is safe
        assertTrue(true, "Concurrent access should not cause exceptions")
    }

    @Test
    fun `should provide consistent results for same inputs`() = runBlocking {
        val path = System.getProperty("user.home")

        val result1 = SystemUtils.getInodeNumber(path)
        val result2 = SystemUtils.getInodeNumber(path)

        assertEquals(result1, result2, "Same path should return same inode result")

        // Git email should be consistent
        val email1 = SystemUtils.getGitUserEmail()
        val email2 = SystemUtils.getGitUserEmail()

        assertEquals(email1, email2, "Git email should be consistent")
    }

    @Test
    fun `should handle empty and null inputs gracefully`() {
        // Test empty string
        val emptyResult = SystemUtils.getInodeNumber("")
        assertNotNull(emptyResult)
        
        // Test with whitespace
        val whitespaceResult = SystemUtils.getInodeNumber("   ")
        assertNotNull(whitespaceResult)
        
        // These should not throw exceptions
    }
}
