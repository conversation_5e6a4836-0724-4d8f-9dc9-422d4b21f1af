# IntelliJ Feature Vector Collector (<PERSON>tlin)

Kotlin implementation of the feature vector collector for IntelliJ IDEA and JetBrains IDEs. Provides identical API to the TypeScript version with additional IntelliJ-specific features.

## API Interface

```kotlin
object IntelliJFeatureVectorCollector {
    suspend fun createFeatures(): Features
}

class Features {
    fun toVector(): Map<Int, String>
}
```

## Usage

```kotlin
import com.augmentcode.featurevector.*
import kotlinx.coroutines.runBlocking

runBlocking {
    val features = IntelliJFeatureVectorCollector.createFeatures()
    val vector = features.toVector()
    // vector contains 48 fields (indices 0-47 + checksum at index 12)
}
```

## Feature Vector Field Contents

The feature vector contains exactly **48 fields** (indices 0-47 + checksum). Each field contains a SHA-256 hash of the canonicalized (lowercase, trimmed) source data.

### VSCode Fields (Empty in IntelliJ Environment)
- **Index 0**: VSCode Version → Empty string
- **Index 1**: VSCode Machine ID → Empty string
- **Index 13**: Telemetry Device ID → Empty string
- **Index 21**: User Data Path Inode → Empty string
- **Index 22**: User Data Machine ID → Empty string
- **Index 23**: Storage URI Path → Empty string

### System Information Fields
- **Index 2**: Operating System → OS family name (e.g., "Windows", "Linux")
- **Index 3**: CPU Model → CPU model name
- **Index 4**: Total Memory → Total system memory in bytes as string
- **Index 5**: CPU Count → Number of logical CPU cores as string
- **Index 6**: Hostname → System hostname
- **Index 7**: Architecture → System architecture (e.g., "x86_64")
- **Index 8**: Username → Current user name
- **Index 9**: MAC Addresses → Comma-separated external MAC addresses
- **Index 10**: OS Release → OS version information
- **Index 11**: Kernel Version → OS kernel/build version

### Identifier Fields
- **Index 12**: Checksum → "v1#" + SHA-256 hash of all other fields
- **Index 14**: Request ID → UUID generated for this request
- **Index 15**: Random Hash → 32-character random hex string
- **Index 16**: OS Machine ID → Platform-specific machine identifier
- **Index 17**: Home Directory Inode → Home directory path
- **Index 18**: Project Root Inode → Current working directory path
- **Index 19**: Git User Email → Git user.email from config
- **Index 20**: SSH Public Key → First found SSH public key content

### Hardware Information Fields
- **Index 24**: GPU Information → JSON string of GPU details
- **Index 25**: Timezone → System timezone identifier
- **Index 26**: Disk Layout → JSON string of disk information
- **Index 27**: System Information → JSON string of system details
- **Index 28**: BIOS Information → JSON string of BIOS details
- **Index 29**: Baseboard Information → JSON string of motherboard details
- **Index 30**: Chassis Information → JSON string of chassis details
- **Index 31**: Baseboard Asset Tag → Motherboard asset tag
- **Index 32**: Chassis Asset Tag → Chassis asset tag
- **Index 33**: CPU Flags → Empty string (OSHI limitation)
- **Index 34**: Memory Module Serials → Empty string (OSHI limitation)
- **Index 35**: USB Device IDs → Empty string (OSHI limitation)
- **Index 36**: Audio Device IDs → Comma-separated audio device identifiers
- **Index 37**: Hypervisor Type → Virtualization platform name
- **Index 38**: System Boot Time → System boot timestamp in milliseconds
- **Index 39**: SSH Known Hosts → Last 5 entries from ~/.ssh/known_hosts

### IntelliJ-Specific Fields (40-47)
- **Index 40**: IntelliJ Installation UID → Deterministic UUID based on installation paths
- **Index 41**: IntelliJ Config Path Inode → Configuration directory path
- **Index 42**: IntelliJ System Path Inode → System/cache directory path
- **Index 43**: IntelliJ Plugins Path Inode → Plugins directory path
- **Index 44**: IntelliJ Installed Plugins Hash → Hash of installed plugins
- **Index 45**: IntelliJ Product Info Hash → Hash of product information (from system properties)
- **Index 46**: IntelliJ License ID → License identifier (from IntelliJ Platform API when available)
- **Index 47**: IntelliJ Build Number → Build number (from IntelliJ Platform API when available)

## Hashing and Canonicalization

All field values are processed identically to the TypeScript version:

1. **Canonicalization**: `input.lowercase().trim()`
2. **SHA-256 Hashing**: Produces 64-character hex string
3. **Empty Values**: Empty strings become `e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855`
4. **Arrays**: Joined with commas, then canonicalized and hashed
5. **Checksum**: `"v1#" + SHA256(sorted_field_values_concatenated)`

## Differences from TypeScript Version

### VSCode Fields
- All VSCode-specific fields (indices 0, 1, 13, 21, 22, 23) are empty strings (hashed)
- This correctly indicates the collector is running outside VSCode

### IntelliJ Fields
- 8 additional fields (indices 40-47) provide IntelliJ-specific fingerprinting
- These fields are unique to the Kotlin version and not present in TypeScript
- 6 out of 8 fields successfully collect data when WebStorm/IntelliJ is installed

### Hardware Limitations
- Some hardware fields (CPU flags, memory serials, USB devices) may be empty due to OSHI library limitations
- Core system information is fully available and identical to TypeScript version

## Hashing and Canonicalization

The implementation uses **identical hashing logic** to the TypeScript version:

- **SHA-256**: Same algorithm and hex encoding
- **Canonicalization**: Lowercase, trim, then hash
- **Array Canonicalization**: Join with commas, then canonicalize
- **Checksum**: "v1#" prefix + SHA-256 of sorted concatenated values

## Cross-Platform Compatibility

The Kotlin implementation works on the same platforms as the TypeScript version:

- **Linux**: Uses `/proc/stat` for boot time, `/etc/machine-id` for machine ID
- **Windows**: Uses PowerShell and WMI for system information
- **macOS**: Uses `sysctl` and `system_profiler` for system data

## Building

### Using Gradle (Current)

```bash
cd clients/common/feature-vector-collector-kotlin
./gradlew build
./gradlew test
```

### Code Obfuscation (Production)

For production deployments, the library includes robust code obfuscation using ProGuard 7.7.0:

```bash
# Build obfuscated version for production
./gradlew buildObfuscated

# Verify obfuscation worked correctly
./gradlew verifyObfuscation

# Just run obfuscation task
./gradlew obfuscateJar
```

The obfuscated JAR will be created in `build/obfuscated/` along with:
- `mapping.txt` - **Critical for debugging** - maps obfuscated names back to original names
- `seeds.txt` - Lists classes/methods that were kept (not obfuscated)
- `usage.txt` - Shows what code was removed during shrinking

**⚠️ Important**: Always keep the `mapping.txt` file safe! You'll need it to deobfuscate stack traces from production.

**Note**: The original `BUILD` file (for Bazel) has been removed to prevent conflicts with Gradle's build directory on case-insensitive filesystems. This ensures compatibility with macOS and Windows development environments.

### Using Bazel (Future)

When Kotlin rules are added to the repository's MODULE.bazel, a new BUILD file will need to be created:

```bash
bazel build //clients/common/feature-vector-collector-kotlin:feature_vector_collector_kotlin
bazel test //clients/common/feature-vector-collector-kotlin:feature_vector_collector_kotlin_test
```

**Note**: The BUILD file was temporarily removed to resolve Gradle build conflicts. It will be restored when Bazel Kotlin support is properly configured.

## Testing

Run the test suite to verify functionality:

```bash
./gradlew test
```

The tests verify:
- Enum value consistency with TypeScript
- Hashing and canonicalization correctness
- Feature vector generation
- System utility functions
- End-to-end feature collection

## Integration

### With IntelliJ Plugin

The Kotlin implementation can be integrated into the IntelliJ plugin by adding it as a dependency in `clients/intellij/build.gradle.kts`:

```kotlin
dependencies {
    implementation(project(":clients:common:feature-vector-collector-kotlin"))
    // ... other dependencies
}
```

### With Other Kotlin/JVM Projects

Add as a Gradle dependency:

```kotlin
dependencies {
    implementation("com.augmentcode:feature-vector-collector-kotlin:1.0.0")
}
```

## Verification

To verify the Kotlin implementation produces identical results to the TypeScript version:

1. Run both implementations on the same system
2. Compare the generated feature vectors field by field
3. Verify checksums match (indicating identical canonicalization)
4. Test across different platforms and configurations

## Known Limitations

The following features are currently simplified and return empty strings (marked as TODO in the code):

- **Memory Module Serial Numbers**: OSHI API usage needs to be corrected for `PhysicalMemory.getSerialNumber()`
- **USB Device IDs**: OSHI API usage needs to be corrected for `UsbDevice.getVendor()` and `UsbDevice.getProductId()`

These limitations do not affect the core functionality of feature vector generation, as the implementation still produces valid 40-field vectors with proper checksums.

## Security Features

### Code Obfuscation

The library includes enterprise-grade code obfuscation powered by ProGuard 7.7.0:

- **Class/Method Renaming**: Internal implementation details are renamed to meaningless short names (a, b, c, etc.)
- **Code Shrinking**: Unused code is automatically removed, reducing JAR size by ~12%
- **Optimization**: Bytecode is optimized for better runtime performance
- **Public API Preservation**: All public APIs remain unchanged for seamless integration

### Obfuscation Benefits

- **Intellectual Property Protection**: Makes reverse engineering significantly more difficult
- **Security Through Obscurity**: Hides internal implementation details and algorithms
- **Reduced Attack Surface**: Smaller JAR size with unused code removed
- **Performance Improvement**: Optimized bytecode runs faster

### Safe Obfuscation Rules

The ProGuard configuration includes comprehensive rules to ensure safe obfuscation:

- Preserves all public APIs for library consumers
- Keeps Kotlin metadata and coroutines functionality
- Maintains compatibility with OSHI, Gson, and other dependencies
- Preserves reflection-based operations
- Keeps debugging information for production troubleshooting

## Future Enhancements

- **Complete OSHI API Integration**: Fix the memory module and USB device information collection
- **Bazel Integration**: Add Kotlin rules to MODULE.bazel for native Bazel builds (BUILD file was removed to fix Gradle compatibility)
- **Performance Optimization**: Optimize system information collection for faster execution
- **Additional Platforms**: Add support for additional operating systems if needed
- **Caching**: Add intelligent caching for expensive system information queries

## Compatibility Notes

- **Inode Numbers**: Like the TypeScript version, these are hashes of path strings, not actual filesystem inodes
- **Machine IDs**: Uses platform-specific methods to get unique machine identifiers
- **System Information**: OSHI provides cross-platform access to system details
- **Async Operations**: Uses Kotlin coroutines for concurrent data collection
